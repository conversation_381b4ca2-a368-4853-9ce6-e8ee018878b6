package context

import (
	"encoding/json"
	"path"
	"regexp"
	"strings"

	"github.com/precize/common"
)

func getRelatedResourceList(resourceContext *ResourceContext, resourceContextDoc common.ResourceContextInsertDoc,
	resourceDocID, entityJSON string) {

	entityJSONMap := make(map[string]any)
	if err := json.Unmarshal([]byte(entityJSON), &entityJSONMap); err != nil {
		return
	}

	switch resourceContextDoc.ResourceType {

	case common.AWS_EC2_RESOURCE_TYPE:

		if subnetID, ok := entityJSONMap["subnetId"].(string); ok && len(subnetID) >= 0 {

			subnetDocID := common.GenerateCombinedHashID(subnetID, common.AWS_SUBNET_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

			assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				subnetID, subnetDocID, common.AWS_SUBNET_RESOURCE_TYPE, true)
		}

		if vpcID, ok := entityJSONMap["vpcId"].(string); ok && len(vpcID) >= 0 {

			vpcDocID := common.GenerateCombinedHashID(vpcID, common.AWS_VPC_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

			assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				vpcID, vpcDocID, common.AWS_VPC_RESOURCE_TYPE, true)
		}

		if securityGroups, ok := entityJSONMap["securityGroups"].([]any); ok {

			for _, secGroup := range securityGroups {

				if sgID, ok := secGroup.(string); ok {

					sgDocID := common.GenerateCombinedHashID(sgID, common.AWS_SG_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						sgID, sgDocID, common.AWS_SG_RESOURCE_TYPE, true)
				}
			}
		}

		if instance, ok := entityJSONMap["instance"].(map[string]any); ok {

			if blockDeviceMappings, ok := instance["blockDeviceMappings"].([]any); ok {

				for _, blockDeviceMapping := range blockDeviceMappings {

					if b, ok := blockDeviceMapping.(map[string]any); ok {

						if ebs, ok := b["ebs"].(map[string]any); ok {

							if ebsVolumeID, ok := ebs["volumeId"].(string); ok && len(ebsVolumeID) > 0 {

								ebsVolumeDocID := common.GenerateCombinedHashID(ebsVolumeID, common.AWS_EBSVOLUME_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

								assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
									ebsVolumeID, ebsVolumeDocID, common.AWS_EBSVOLUME_RESOURCE_TYPE, true)
							}
						}
					}
				}
			}

			if networkInterfaces, ok := instance["networkInterfaces"].([]any); ok {

				for _, networkInterface := range networkInterfaces {

					if networkInterfaceMap, ok := networkInterface.(map[string]any); ok {

						if networkInterfaceID, ok := networkInterfaceMap["networkInterfaceId"].(string); ok && len(networkInterfaceID) > 0 {

							networkInterfaceDocID := common.GenerateCombinedHashID(networkInterfaceID, common.NETWORKINTERFACE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

							assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
								networkInterfaceID, networkInterfaceDocID, common.NETWORKINTERFACE_RESOURCE_TYPE, true)

						}
					}
				}
			}

			if ipDetails, ok := entityJSONMap["publicIpAddress"].(string); ok && len(ipDetails) >= 0 {

				ipDetailsDocID := common.GenerateCombinedHashID(ipDetails, common.IPDETAILS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

				assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
					ipDetails, ipDetailsDocID, common.IPDETAILS_RESOURCE_TYPE, true)
			}
		}

		if keyPairID, ok := entityJSONMap["keyPairId"].(string); ok && len(keyPairID) >= 0 {

			keyPairID = strings.ToLower(keyPairID)
			keyPairDocID := common.GenerateCombinedHashID(keyPairID, common.AWS_EC2KEYPAIR_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

			assignRelatedResourcePriority(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				keyPairID, keyPairDocID, common.AWS_EC2KEYPAIR_RESOURCE_TYPE, true)
		}

		if amiID, ok := entityJSONMap["imageId"].(string); ok && len(amiID) >= 0 {

			amiID = strings.ToLower(amiID)
			amiDocID := common.GenerateCombinedHashID(amiID, common.AWS_AMI_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

			assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				amiID, amiDocID, common.AWS_AMI_RESOURCE_TYPE, true)
		}

		if clusterArn, ok := entityJSONMap["clusterArn"].(string); ok && len(clusterArn) >= 0 {

			cluster := strings.ToLower(clusterArn)
			clusterDocID := common.GenerateCombinedHashID(cluster, common.AWS_EKSCLUSTER_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				cluster, clusterDocID, common.AWS_EKSCLUSTER_RESOURCE_TYPE, true)
		}

	case common.AWS_SUBNET_RESOURCE_TYPE, common.AWS_ROUTETABLE_RESOURCE_TYPE:

		if vpcID, ok := entityJSONMap["vpcId"].(string); ok && len(vpcID) >= 0 {

			vpcDocID := common.GenerateCombinedHashID(vpcID, common.AWS_VPC_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

			assignRelatedParentResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				vpcID, vpcDocID, common.AWS_VPC_RESOURCE_TYPE, true)
		}

		if internetGateways, ok := entityJSONMap["gatewayIds"].([]any); ok {

			for _, internetGateway := range internetGateways {

				if internetGatewayID, ok := internetGateway.(string); ok && len(internetGatewayID) > 0 {

					if internetGatewayID != "local" {
						internetGatewayDocID := common.GenerateCombinedHashID(internetGatewayID, common.AWS_INTERNETGATEWAY_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

						assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
							internetGatewayID, internetGatewayDocID, common.AWS_INTERNETGATEWAY_RESOURCE_TYPE, true)
					}
				}
			}
		}

	case common.AWS_EBSSNAPSHOT_RESOURCE_TYPE:

		if ebsVolumeID, ok := entityJSONMap["volumeId"].(string); ok && len(ebsVolumeID) >= 0 {

			if ebsVolumeID == "vol-ffffffff" {
				// If snapshot is a copy of another snapshot, volumeId will be as above (no ebs volume exists like that)
				if snapshotInfo, ok := entityJSONMap["snapshot"].(map[string]any); ok {
					if desc, ok := snapshotInfo["description"].(string); ok && len(desc) > 0 {
						regExpSnap := regexp.MustCompile(`snap-[a-zA-Z0-9]+`)
						snapshotID := regExpSnap.FindString(desc)

						if len(snapshotID) > 0 {
							snapshotDocID := common.GenerateCombinedHashID(snapshotID, common.AWS_EBSSNAPSHOT_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

							assignRelatedParentResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
								snapshotID, snapshotDocID, common.AWS_EBSSNAPSHOT_RESOURCE_TYPE, true)
						}
					}
				}
			} else {
				ebsVolumeDocID := common.GenerateCombinedHashID(ebsVolumeID, common.AWS_EBSVOLUME_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

				assignRelatedParentResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
					ebsVolumeID, ebsVolumeDocID, common.AWS_EBSVOLUME_RESOURCE_TYPE, true)
			}
		}

		if kmsKeyID, ok := entityJSONMap["kmsKeyId"].(string); ok && len(kmsKeyID) > 0 {
			kmsKeyID = strings.ToLower(kmsKeyID)
			kmsKeyDocID := common.GenerateCombinedHashID(kmsKeyID, common.AWS_KMS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

			assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				kmsKeyID, kmsKeyDocID, common.AWS_KMS_RESOURCE_TYPE, true)
		}

	case common.AWS_AMI_RESOURCE_TYPE:

		if amiSnapshots, ok := entityJSONMap["snapshots"].([]any); ok {

			for _, amiSnapshot := range amiSnapshots {

				if snapshotID, ok := amiSnapshot.(string); ok && len(snapshotID) > 0 {

					snapshotDocID := common.GenerateCombinedHashID(snapshotID, common.AWS_EBSSNAPSHOT_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

					assignRelatedParentResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						snapshotID, snapshotDocID, common.AWS_EBSSNAPSHOT_RESOURCE_TYPE, true)
				}
			}
		}

	case common.AWS_ELASTICIP_RESOURCE_TYPE:

		if instanceID, ok := entityJSONMap["instanceId"].(string); ok && len(instanceID) >= 0 {

			instanceDocID := common.GenerateCombinedHashID(instanceID, common.AWS_EC2_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

			assignRelatedParentResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				instanceID, instanceDocID, common.AWS_EC2_RESOURCE_TYPE, true)
		}

		if ipDetails, ok := entityJSONMap["publicIp"].(string); ok && len(ipDetails) >= 0 {

			ipDetailsDocID := common.GenerateCombinedHashID(ipDetails, common.IPDETAILS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

			assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				ipDetails, ipDetailsDocID, common.IPDETAILS_RESOURCE_TYPE, true)
		}

		if nwInterfaceID, ok := entityJSONMap["networkInterfaceId"].(string); ok && len(nwInterfaceID) >= 0 {

			nwInterfaceDocID := common.GenerateCombinedHashID(nwInterfaceID, common.NETWORKINTERFACE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

			assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				nwInterfaceID, nwInterfaceDocID, common.NETWORKINTERFACE_RESOURCE_TYPE, true)
		}

	case common.AWS_S3_RESOURCE_TYPE:

		if encryption, ok := entityJSONMap["encryption"].(map[string]any); ok {

			if serverSideEncryptionConfiguration, ok := encryption["serverSideEncryptionConfiguration"].(map[string]any); ok {

				if rules, ok := serverSideEncryptionConfiguration["rules"].([]any); ok {

					for _, rule := range rules {
						if ruleMap, ok := rule.(map[string]any); ok {
							if applyServerSideEncryptionByDefault, ok := ruleMap["applyServerSideEncryptionByDefault"].(map[string]any); ok {
								if kmsKeyID, ok := applyServerSideEncryptionByDefault["kmsmasterKeyID"].(string); ok && len(kmsKeyID) >= 0 {

									kmsKeyID = strings.ToLower(kmsKeyID)
									kmsKeyDocID := common.GenerateCombinedHashID(kmsKeyID, common.AWS_KMS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

									assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
										kmsKeyID, kmsKeyDocID, common.AWS_KMS_RESOURCE_TYPE, true)
								}
							}
						}
					}
				}
			}
		}

	case common.AWS_SQS_RESOURCE_TYPE:
		if attributes, ok := entityJSONMap["attributes"].(map[string]any); ok {
			if kmsKeyID, ok := attributes["KmsMasterKeyId"].(string); ok && len(kmsKeyID) > 0 {
				kmsKeyID = strings.ToLower(kmsKeyID)
				kmsDocID := common.GenerateCombinedHashID(kmsKeyID, common.AWS_KMS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, kmsKeyID, kmsDocID, common.AWS_KMS_RESOURCE_TYPE, true)
			}
		}

	case common.AWS_SNS_RESOURCE_TYPE:
		if topicAttributes, ok := entityJSONMap["topicAttributes"].(map[string]any); ok {
			if kmsKeyID, ok := topicAttributes["KmsMasterKeyId"].(string); ok && len(kmsKeyID) > 0 {
				kmsKeyID = strings.ToLower(kmsKeyID)
				kmsDocID := common.GenerateCombinedHashID(kmsKeyID, common.AWS_KMS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, kmsKeyID, kmsDocID, common.AWS_KMS_RESOURCE_TYPE, true)
			}
		}

	case common.AWS_RDS_RESOURCE_TYPE:

		if vpcSecurityGroupIDs, ok := entityJSONMap["vpcSecurityGroupIds"].([]any); ok {

			for _, vpcSecurityGroupID := range vpcSecurityGroupIDs {

				if sgID, ok := vpcSecurityGroupID.(string); ok && len(sgID) > 0 {

					sgDocID := common.GenerateCombinedHashID(sgID, common.AWS_SG_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						sgID, sgDocID, common.AWS_SG_RESOURCE_TYPE, true)
				}
			}
		}

		if dbInstance, ok := entityJSONMap["dbInstance"].(map[string]any); ok {

			if dbSubnetGroup, ok := dbInstance["dbsubnetGroup"].(map[string]any); ok {

				if vpcID, ok := dbSubnetGroup["vpcId"].(string); ok && len(vpcID) > 0 {

					vpcDocID := common.GenerateCombinedHashID(vpcID, common.AWS_VPC_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						vpcID, vpcDocID, common.AWS_VPC_RESOURCE_TYPE, true)
				}

				if dbSubnets, ok := dbSubnetGroup["subnets"].([]any); ok {

					for _, dbSubnet := range dbSubnets {

						if dbSubnetMap, ok := dbSubnet.(map[string]any); ok {

							if subnetID, ok := dbSubnetMap["subnetIdentifier"].(string); ok && len(subnetID) > 0 {

								subnetDocID := common.GenerateCombinedHashID(subnetID, common.AWS_SUBNET_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

								assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
									subnetID, subnetDocID, common.AWS_SUBNET_RESOURCE_TYPE, true)
							}
						}
					}
				}
			}
		}

		if dbInstance, ok := entityJSONMap["dbInstance"].(map[string]any); ok {
			if kmsKeyID, ok := dbInstance["kmsKeyId"].(string); ok && len(kmsKeyID) > 0 {
				kmsKeyID = strings.ToLower(kmsKeyID)
				kmsKeyDocID := common.GenerateCombinedHashID(kmsKeyID, common.AWS_KMS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, kmsKeyID, kmsKeyDocID, common.AWS_KMS_RESOURCE_TYPE, true)
			}
		}

	case common.AWS_RDSSNAPSHOT_RESOURCE_TYPE:

		if dbInstanceID, ok := entityJSONMap["dbInstanceId"].(string); ok {
			dbInstanceID = strings.ToLower(dbInstanceID)
			if engine, ok := entityJSONMap["engine"].(string); ok && strings.Contains(engine, "aurora") {
				auroraDocID := common.GenerateCombinedHashID(dbInstanceID, common.AWS_AURORA_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				assignRelatedParentResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
					dbInstanceID, auroraDocID, common.AWS_AURORA_RESOURCE_TYPE, true)
			} else {
				rdsDocID := common.GenerateCombinedHashID(dbInstanceID, common.AWS_RDS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				assignRelatedParentResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
					dbInstanceID, rdsDocID, common.AWS_RDS_RESOURCE_TYPE, true)
			}
		}

	case common.AWS_RDSCLUSTERSNAPSHOT_RESOURCE_TYPE:

		if rdsClusterID, ok := entityJSONMap["dbClusterIdentifier"].(string); ok {

			rdsClusterID = strings.ToLower(rdsClusterID)

			rdsDocID := common.GenerateCombinedHashID(rdsClusterID, common.AWS_RDSCLUSTER_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

			assignRelatedParentResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				rdsClusterID, rdsDocID, common.AWS_RDSCLUSTER_RESOURCE_TYPE, true)

		}

	case common.AWS_AURORA_RESOURCE_TYPE:

		if dbInstance, ok := entityJSONMap["dbInstance"].(map[string]any); ok {
			if kmsKeyID, ok := dbInstance["kmsKeyId"].(string); ok && len(kmsKeyID) > 0 {
				kmsKeyID = strings.ToLower(kmsKeyID)
				kmsKeyDocID := common.GenerateCombinedHashID(kmsKeyID, common.AWS_KMS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, kmsKeyID, kmsKeyDocID, common.AWS_KMS_RESOURCE_TYPE, true)
			}
		}

	case common.AWS_LAMBDA_RESOURCE_TYPE:

		if functionConfig, ok := entityJSONMap["functionConfiguration"].(map[string]any); ok {
			if vpcConfig, ok := functionConfig["vpcConfig"].(map[string]any); ok {
				if subnetIDs, ok := vpcConfig["subnetIds"].([]any); ok {
					for _, subnetID := range subnetIDs {
						if subnetIDString, ok := subnetID.(string); ok && len(subnetIDString) > 0 {
							subnetDocID := common.GenerateCombinedHashID(subnetIDString, common.AWS_SUBNET_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
							assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
								subnetIDString, subnetDocID, common.AWS_SUBNET_RESOURCE_TYPE, true)
						}
					}
				}

				if securityGroupIDs, ok := vpcConfig["securityGroupIds"].([]any); ok {
					for _, securityGroupID := range securityGroupIDs {
						if securityGroupIDString, ok := securityGroupID.(string); ok && len(securityGroupIDString) > 0 {
							securityGroupDocID := common.GenerateCombinedHashID(securityGroupIDString, common.AWS_SG_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
							assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
								securityGroupIDString, securityGroupDocID, common.AWS_SG_RESOURCE_TYPE, true)
						}
					}
				}

				if vpcID, ok := vpcConfig["vpcId"].(string); ok && len(vpcID) > 0 {
					vpcDocID := common.GenerateCombinedHashID(vpcID, common.AWS_VPC_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						vpcID, vpcDocID, common.AWS_VPC_RESOURCE_TYPE, true)
				}
			}

			if roleArn, ok := functionConfig["role"].(string); ok && len(roleArn) > 0 {
				splitArn := strings.Split(roleArn, "/")
				role := strings.ToLower(splitArn[len(splitArn)-1])

				roleDocID := common.GenerateCombinedHashID(role, common.AWS_IAM_ROLE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
					role, roleDocID, common.AWS_IAM_ROLE_RESOURCE_TYPE, true)
			}
		}

		if fnConfig, ok := entityJSONMap["functionConfiguration"].(map[string]any); ok {
			if kmsKeyArn, ok := fnConfig["kmskeyArn"].(string); ok && len(kmsKeyArn) > 0 {
				kmsKeyArn = strings.ToLower(kmsKeyArn)
				kmsDocID := common.GenerateCombinedHashID(kmsKeyArn, common.AWS_KMS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, kmsKeyArn, kmsDocID, common.AWS_KMS_RESOURCE_TYPE, true)
			}
		}

	case common.AWS_APIGATEWAY_RESOURCE_TYPE:

		if attachedLambdas, ok := entityJSONMap["attachedLambdas"].([]any); ok {
			for _, lambda := range attachedLambdas {
				if lambdaString, ok := lambda.(string); ok && len(lambdaString) > 0 {
					lambdaDocID := common.GenerateCombinedHashID(lambdaString, common.AWS_LAMBDA_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						lambdaString, lambdaDocID, common.AWS_LAMBDA_RESOURCE_TYPE, true)
				}
			}
		}

	case common.AWS_CONTAINERIMAGE_RESOURCE_TYPE:

		if repositoryID, ok := entityJSONMap["repositoryArn"].(string); ok && len(repositoryID) > 0 {
			repositoryID = strings.ToLower(repositoryID)
			repositoryDocID := common.GenerateCombinedHashID(repositoryID, common.AWS_CONTAINERREPOSITORY_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			assignRelatedParentResourceOneWay(resourceContext, resourceDocID, repositoryID,
				repositoryDocID, common.AWS_CONTAINERREPOSITORY_RESOURCE_TYPE, true)
		}

	case common.AWS_CONTAINERREPOSITORY_RESOURCE_TYPE:

		if repositoryUri, ok := entityJSONMap["repositoryUri"].(string); ok && len(repositoryUri) > 0 {
			registryID := strings.ToLower(path.Dir(repositoryUri))
			registryDocID := common.GenerateCombinedHashID(registryID, common.AWS_CONTAINERREGISTRY_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			assignRelatedParentResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				registryID, registryDocID, common.AWS_CONTAINERREGISTRY_RESOURCE_TYPE, true)
		}

	case common.AWS_IAM_ROLE_RESOURCE_TYPE:

		if attachedPolicies, ok := entityJSONMap["attachedPolicies"].([]any); ok {
			for _, policy := range attachedPolicies {
				if policyMap, ok := policy.(map[string]any); ok {
					if policyArn, ok := policyMap["policyArn"].(string); ok && len(policyArn) > 0 {
						policyArnSplit := strings.Split(policyArn, "/")
						policyID := strings.ToLower(policyArnSplit[len(policyArnSplit)-1])
						policyDocID := common.GenerateCombinedHashID(policyID, common.AWS_IAMPOLICY_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
						assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
							policyID, policyDocID, common.AWS_IAMPOLICY_RESOURCE_TYPE, false)

						// TODO: For custom created policies, contextual can be true
					}
				}
			}
		}

	case common.AWS_DYNAMODB_RESOURCE_TYPE:
		if tableDescription, ok := entityJSONMap["tableDescription"].(map[string]any); ok {
			if sseDesc, ok := tableDescription["ssedescription"].(map[string]any); ok {
				if kmsArn, ok := sseDesc["kmsmasterKeyArn"].(string); ok && len(kmsArn) > 0 {
					kmsArn = strings.ToLower(kmsArn)
					kmsDocID := common.GenerateCombinedHashID(kmsArn, common.AWS_KMS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, kmsArn, kmsDocID, common.AWS_KMS_RESOURCE_TYPE, true)
				}
			}
		}

	case common.AWS_SAGEMAKERENDPOINTCONFIG_RESOURCE_TYPE:

		if s3Buckets, ok := entityJSONMap["s3bucketNames"].([]any); ok {
			for _, s3Bucket := range s3Buckets {
				if bucket, ok := s3Bucket.(string); ok && len(bucket) > 0 {
					bucket = strings.ToLower(bucket)
					// Bucket can be from any account
					s3Doc, err := common.GetCloudResourceDocumentForEntityIDAndType(bucket, common.AWS_S3_RESOURCE_TYPE, resourceContextDoc.TenantID, resourceContext.LastCollectedAt)
					if err != nil {
						break
					}

					if accountID, ok := s3Doc["accountId"].(string); ok && len(accountID) > 0 {
						bucketDocID := common.GenerateCombinedHashID(bucket, common.AWS_S3_RESOURCE_TYPE, accountID, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
						assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
							bucket, bucketDocID, common.AWS_S3_RESOURCE_TYPE, true)
					}
				}
			}
		}

		if endpoints, ok := entityJSONMap["endpoints"].([]any); ok {
			for _, endpoint := range endpoints {
				if endpointID, ok := endpoint.(string); ok && len(endpointID) > 0 {
					endpointID = strings.ToLower(endpointID)
					endpointDocID := common.GenerateCombinedHashID(endpointID, common.AWS_SAGEMAKERENDPOINT_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						endpointID, endpointDocID, common.AWS_SAGEMAKERENDPOINT_RESOURCE_TYPE, true)
				}
			}
		}

	case common.AWS_SAGEMAKERTRAININGJOB_RESOURCE_TYPE, common.AWS_SAGEMAKERMODEL_RESOURCE_TYPE:

		if roleArn, ok := entityJSONMap["roleArn"].(string); ok && len(roleArn) > 0 {
			splitArn := strings.Split(roleArn, "/")
			role := strings.ToLower(splitArn[len(splitArn)-1])
			roleDocID := common.GenerateCombinedHashID(role, common.AWS_IAM_ROLE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				role, roleDocID, common.AWS_IAM_ROLE_RESOURCE_TYPE, true)
		}

		if s3Buckets, ok := entityJSONMap["s3bucketNames"].([]any); ok {
			for _, s3Bucket := range s3Buckets {
				if bucket, ok := s3Bucket.(string); ok && len(bucket) > 0 {
					bucket = strings.ToLower(bucket)
					// Bucket can be from any account
					s3Doc, err := common.GetCloudResourceDocumentForEntityIDAndType(bucket, common.AWS_S3_RESOURCE_TYPE, resourceContextDoc.TenantID, resourceContext.LastCollectedAt)
					if err != nil {
						break
					}

					if accountID, ok := s3Doc["accountId"].(string); ok && len(accountID) > 0 {
						bucketDocID := common.GenerateCombinedHashID(bucket, common.AWS_S3_RESOURCE_TYPE, accountID, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
						assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
							bucket, bucketDocID, common.AWS_S3_RESOURCE_TYPE, true)
					}
				}
			}
		}

		if containerImageTags, ok := entityJSONMap["containerImageTags"].([]any); ok {
			for _, containerImageTag := range containerImageTags {
				if imageTag, ok := containerImageTag.(string); ok && len(imageTag) > 0 {
					imageTag = strings.ToLower(imageTag)
					// Image can be from any account
					repo := path.Dir(imageTag)
					tag := path.Base(imageTag)
					imageDocs, err := common.GetCloudResourceDocumentFromEntityAndJSON(repo, common.AWS_CONTAINERIMAGE_RESOURCE_TYPE, tag, resourceContextDoc.TenantID, resourceContext.LastCollectedAt)
					if err != nil {
						break
					}

					for _, imageDoc := range imageDocs {

						if imageEntityJSONString, ok := imageDoc["entityJson"].(string); ok && len(imageEntityJSONString) > 0 {

							imageEntityJSONMap := make(map[string]any)

							if err = json.Unmarshal([]byte(imageEntityJSONString), &imageEntityJSONMap); err != nil {
								continue
							}

							if imageTags, ok := imageEntityJSONMap["imageTags"].(string); ok && len(imageTags) > 0 {

								if strings.Contains(imageTags, tag) {
									if accountID, ok := imageDoc["accountId"].(string); ok && len(accountID) > 0 {
										if containerImageId, ok := imageDoc["entityId"].(string); ok {
											imageDocID := common.GenerateCombinedHashID(containerImageId, common.AWS_CONTAINERIMAGE_RESOURCE_TYPE, accountID, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
											assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
												containerImageId, imageDocID, common.AWS_CONTAINERIMAGE_RESOURCE_TYPE, true)
										}
									}

									break
								}
							}
						}
					}
				}
			}
		}

		if containerImageDigests, ok := entityJSONMap["containerImageDigests"].([]any); ok {
			for _, containerImageDigest := range containerImageDigests {
				if digest, ok := containerImageDigest.(string); ok && len(digest) > 0 {
					digest = strings.ToLower(digest)
					// Image can be from any account
					digestDoc, err := common.GetCloudResourceDocumentForEntityIDAndType(digest, common.AWS_CONTAINERIMAGE_RESOURCE_TYPE, resourceContextDoc.TenantID, resourceContext.LastCollectedAt)
					if err != nil {
						break
					}

					if accountID, ok := digestDoc["accountId"].(string); ok && len(accountID) > 0 {
						digestDocID := common.GenerateCombinedHashID(digest, common.AWS_CONTAINERIMAGE_RESOURCE_TYPE, accountID, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
						assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
							digest, digestDocID, common.AWS_CONTAINERIMAGE_RESOURCE_TYPE, true)
					}
				}
			}
		}

		if resourceContextDoc.ResourceType == common.AWS_SAGEMAKERMODEL_RESOURCE_TYPE {

			if trainingJob, ok := entityJSONMap["trainingJob"].(string); ok && len(trainingJob) >= 0 {

				trainingJob = strings.ToLower(trainingJob)
				trainingJobDocId := common.GenerateCombinedHashID(trainingJob, common.AWS_SAGEMAKERTRAININGJOB_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
					trainingJob, trainingJobDocId, common.AWS_SAGEMAKERTRAININGJOB_RESOURCE_TYPE, true)
			}

			if endpointConfigs, ok := entityJSONMap["endpointConfigurations"].([]any); ok {
				for _, endpointConfig := range endpointConfigs {
					if endpointConfigId, ok := endpointConfig.(string); ok && len(endpointConfigId) > 0 {
						endpointConfigId = strings.ToLower(endpointConfigId)
						endpointConfigDocID := common.GenerateCombinedHashID(endpointConfigId, common.AWS_SAGEMAKERENDPOINTCONFIG_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
						assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
							endpointConfigId, endpointConfigDocID, common.AWS_SAGEMAKERENDPOINTCONFIG_RESOURCE_TYPE, true)
					}
				}
			}

			if tags, ok := entityJSONMap["tags"].([]any); ok {
				for _, tag := range tags {
					if tagMap, ok := tag.(map[string]any); ok {
						if tagKey, ok := tagMap["key"].(string); ok && len(tagKey) > 0 {
							if tagValue, ok := tagMap["value"].(string); ok && len(tagValue) > 0 {

								if tagKey == "sagemaker:domain-arn" {
									parts := strings.Split(tagValue, "/")
									sagemakerDomainId := strings.ToLower(parts[len(parts)-1])

									sagemakerDomainDocID := common.GenerateCombinedHashID(sagemakerDomainId, common.AWS_AISAGEMAKERDOMAIN_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
									assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
										sagemakerDomainId, sagemakerDomainDocID, common.AWS_AISAGEMAKERDOMAIN_RESOURCE_TYPE, true)
								}

								if tagKey == "sagemaker:space-arn" {
									parts := strings.Split(tagValue, "/")
									sagemakerNotebookInstanceId := strings.ToLower(parts[len(parts)-1])

									sagemakerNotebookInstanceDocId := common.GenerateCombinedHashID(sagemakerNotebookInstanceId, common.AWS_AISAGEMAKERNOTEBOOKINSTANCE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
									assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
										sagemakerNotebookInstanceId, sagemakerNotebookInstanceDocId, common.AWS_AISAGEMAKERNOTEBOOKINSTANCE_RESOURCE_TYPE, true)
								}
							}
						}
					}
				}
			}

			if endpoints, ok := entityJSONMap["endpoints"].([]any); ok {
				for _, endpoint := range endpoints {
					if endpointID, ok := endpoint.(string); ok && len(endpointID) > 0 {

						endpointID = strings.ToLower(endpointID)
						endpointDocID := common.GenerateCombinedHashID(endpointID, common.AWS_SAGEMAKERENDPOINT_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
						assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
							endpointID, endpointDocID, common.AWS_SAGEMAKERENDPOINT_RESOURCE_TYPE, true)
					}
				}
			}
		}

		if resourceContextDoc.ResourceType == common.AWS_SAGEMAKERTRAININGJOB_RESOURCE_TYPE {

			if artifacts, ok := entityJSONMap["artifactIds"].([]any); ok {
				for _, artifact := range artifacts {
					if artifactID, ok := artifact.(string); ok && len(artifactID) > 0 {
						artifactID = strings.ToLower(artifactID)
						artifactDocID := common.GenerateCombinedHashID(artifactID, common.AWS_SAGEMAKERARTIFACT_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
						assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
							artifactID, artifactDocID, common.AWS_SAGEMAKERARTIFACT_RESOURCE_TYPE, true)
					}
				}
			}
		}

	case common.AWS_BEDROCKPROMPT_RESOURCE_TYPE, common.AWS_BEDROCKEVALJOB_RESOURCE_TYPE:

		if foundationModelIDs, ok := entityJSONMap["foundationModelIds"].([]any); ok {
			for _, foundationModelID := range foundationModelIDs {
				if bedrockModel, ok := foundationModelID.(string); ok && len(bedrockModel) > 0 {
					bedrockModel = strings.ToLower(bedrockModel)
					bedrockModelDocID := common.GenerateCombinedHashID(bedrockModel, common.AWS_BEDROCKMODEL_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						bedrockModel, bedrockModelDocID, common.AWS_BEDROCKMODEL_RESOURCE_TYPE, true)
				}
			}
		}

	case common.AWS_BEDROCKAGENT_RESOURCE_TYPE, common.AWS_BEDROCKPROVMODELTHROUGHPUT_RESOURCE_TYPE:

		if bedrockModel, ok := entityJSONMap["foundationModelId"].(string); ok && len(bedrockModel) > 0 {
			bedrockModel = strings.ToLower(bedrockModel)
			bedrockModelDocID := common.GenerateCombinedHashID(bedrockModel, common.AWS_BEDROCKMODEL_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				bedrockModel, bedrockModelDocID, common.AWS_BEDROCKMODEL_RESOURCE_TYPE, true)
		}

		if resourceContextDoc.ResourceType == common.AWS_BEDROCKAGENT_RESOURCE_TYPE {
			if bedrockGuardrailConfig, ok := entityJSONMap["guardrailConfiguration"].(map[string]any); ok {
				if bedrockGuardrailId, ok := bedrockGuardrailConfig["guardrailIdentifier"].(string); ok && len(bedrockGuardrailId) > 0 {
					bedrockGuardrailId = strings.ToLower(bedrockGuardrailId)
					bedrockGuardrailDocId := common.GenerateCombinedHashID(bedrockGuardrailId, common.AWS_BEDROCKGUARDRAIL_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						bedrockGuardrailId, bedrockGuardrailDocId, common.AWS_BEDROCKGUARDRAIL_RESOURCE_TYPE, true)
				}
			}

			if knowledgeBaseIDs, ok := entityJSONMap["knowledgeBaseIds"].([]any); ok {
				for _, knowledgeBase := range knowledgeBaseIDs {
					if knowledgeBaseID, ok := knowledgeBase.(string); ok && len(knowledgeBaseID) > 0 {
						knowledgeBaseID = strings.ToLower(knowledgeBaseID)
						knowledgeBaseIDDocID := common.GenerateCombinedHashID(knowledgeBaseID, common.AWS_BEDROCKKNOWLEDGEBASE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
						assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
							knowledgeBaseID, knowledgeBaseIDDocID, common.AWS_BEDROCKKNOWLEDGEBASE_RESOURCE_TYPE, true)
					}
				}
			}
		}

	case common.AWS_BEDROCKKNOWLEDGEBASE_RESOURCE_TYPE:

		if modelIDs, ok := entityJSONMap["modelIds"].([]any); ok {
			for _, foundationModelID := range modelIDs {
				if bedrockModel, ok := foundationModelID.(string); ok && len(bedrockModel) > 0 {
					bedrockModel = strings.ToLower(bedrockModel)
					bedrockModelDocID := common.GenerateCombinedHashID(bedrockModel, common.AWS_BEDROCKMODEL_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						bedrockModel, bedrockModelDocID, common.AWS_BEDROCKMODEL_RESOURCE_TYPE, true)
				}
			}
		}

		if roleArn, ok := entityJSONMap["roleArn"].(string); ok && len(roleArn) > 0 {
			splitArn := strings.Split(roleArn, "/")
			role := strings.ToLower(splitArn[len(splitArn)-1])
			roleDocID := common.GenerateCombinedHashID(role, common.AWS_IAM_ROLE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				role, roleDocID, common.AWS_IAM_ROLE_RESOURCE_TYPE, true)
		}

		if storageConfiguration, ok := entityJSONMap["storageConfiguration"].(map[string]any); ok {
			if opensearchConfig, ok := storageConfiguration["opensearchServerlessConfiguration"].(map[string]any); ok {
				if collectionArn, ok := opensearchConfig["collectionArn"].(string); ok && len(collectionArn) > 0 {

					splitCollectionArn := strings.Split(collectionArn, "/")
					opensearchID := splitCollectionArn[len(splitCollectionArn)-1]
					opensearchID = strings.ToLower(opensearchID)

					openSearchDocID := common.GenerateCombinedHashID(opensearchID, common.AWS_OPENSEARCHSERVERLESSCOLLECTION_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						opensearchID, openSearchDocID, common.AWS_OPENSEARCHSERVERLESSCOLLECTION_RESOURCE_TYPE, true)
				}
			}
		}

	case common.AWS_BEDROCKAGENTALIASES_RESOURCE_TYPE:

		if agentID, ok := entityJSONMap["agentId"].(string); ok && len(agentID) > 0 {
			agentID = strings.ToLower(agentID)
			agentDocID := common.GenerateCombinedHashID(agentID, common.AWS_BEDROCKAGENT_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				agentID, agentDocID, common.AWS_BEDROCKAGENT_RESOURCE_TYPE, true)
		}

	case common.AWS_KNOWLEDGEBASEDATASOURCE_RESOURCE_TYPE:

		if knowledgeBaseID, ok := entityJSONMap["knowledgeBaseId"].(string); ok && len(knowledgeBaseID) > 0 {
			knowledgeBaseID = strings.ToLower(knowledgeBaseID)
			agentDocID := common.GenerateCombinedHashID(knowledgeBaseID, common.AWS_BEDROCKKNOWLEDGEBASE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				knowledgeBaseID, agentDocID, common.AWS_BEDROCKKNOWLEDGEBASE_RESOURCE_TYPE, true)
		}

		if s3Buckets, ok := entityJSONMap["s3BucketNames"].([]any); ok {
			for _, s3Bucket := range s3Buckets {
				if bucket, ok := s3Bucket.(string); ok && len(bucket) > 0 {
					bucket = strings.ToLower(bucket)
					// Bucket can be from any account
					s3Doc, err := common.GetCloudResourceDocumentForEntityIDAndType(bucket, common.AWS_S3_RESOURCE_TYPE, resourceContextDoc.TenantID, resourceContext.LastCollectedAt)
					if err != nil {
						break
					}

					if accountID, ok := s3Doc["accountId"].(string); ok && len(accountID) > 0 {
						bucketDocID := common.GenerateCombinedHashID(bucket, common.AWS_S3_RESOURCE_TYPE, accountID, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
						assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
							bucket, bucketDocID, common.AWS_S3_RESOURCE_TYPE, true)
					}
				}
			}
		}

	case common.AWS_NETWORKACL_RESOURCE_TYPE:
		if vpcID, ok := entityJSONMap["vpcId"].(string); ok && len(vpcID) >= 0 {
			vpcDocID := common.GenerateCombinedHashID(vpcID, common.AWS_VPC_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

			assignRelatedParentResourceOneWay(resourceContext, resourceContextDoc.ResourceID, vpcID, vpcDocID, common.AWS_EC2_RESOURCE_TYPE, true)
		}

		if subnets, ok := entityJSONMap["subnetIds"].([]any); ok {

			for _, subnet := range subnets {
				if subnetID, ok := subnet.(string); ok && len(subnetID) > 0 {
					subnetDocID := common.GenerateCombinedHashID(subnetID, common.AWS_SUBNET_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						subnetID, subnetDocID, common.AWS_SUBNET_RESOURCE_TYPE, true)
				}
			}
		}

	case common.NETWORKINTERFACE_RESOURCE_TYPE:

		// AWS

		if networkInterfaceMap, ok := entityJSONMap["networkInterface"].(map[string]any); ok {
			if subnetID, ok := networkInterfaceMap["subnetId"].(string); ok && len(subnetID) > 0 {
				subnetDocID := common.GenerateCombinedHashID(subnetID, common.AWS_SUBNET_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

				assignRelatedParentResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
					subnetID, subnetDocID, common.AWS_SUBNET_RESOURCE_TYPE, true)
			}

			if vpcID, ok := networkInterfaceMap["vpcId"].(string); ok && len(vpcID) > 0 {
				vpcDocID := common.GenerateCombinedHashID(vpcID, common.AWS_VPC_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

				assignRelatedParentResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
					vpcID, vpcDocID, common.AWS_VPC_RESOURCE_TYPE, true)
			}

			if securityGroups, ok := networkInterfaceMap["groups"].([]any); ok {
				for _, securityGroup := range securityGroups {
					if sg, ok := securityGroup.(map[string]any); ok {
						if sgID, ok := sg["groupId"].(string); ok && len(sgID) > 0 {
							sgDocID := common.GenerateCombinedHashID(sgID, common.AWS_SG_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

							assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
								sgID, sgDocID, common.AWS_SG_RESOURCE_TYPE, true)
						}
					}
				}
			}
		}

		// Azure

		if nwProperties, ok := entityJSONMap["properties"].(map[string]any); ok {

			if nwSecurityGroup, ok := nwProperties["networkSecurityGroup"].(map[string]any); ok {

				if nsgID, ok := nwSecurityGroup["id"].(string); ok && len(nsgID) > 0 {

					nsgID = strings.ToLower(nsgID)

					nsgDocID := common.GenerateCombinedHashID(nsgID, common.AZURE_NSG_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						nsgID, nsgDocID, common.AZURE_NSG_RESOURCE_TYPE, true)
				}
			}

			if ipConfigs, ok := nwProperties["ipConfigurations"].([]any); ok {

				for _, ipConfig := range ipConfigs {

					if ipConfigMap, ok := ipConfig.(map[string]any); ok {

						if ipConfigProperties, ok := ipConfigMap["properties"].(map[string]any); ok {

							if subnet, ok := ipConfigProperties["subnet"].(map[string]any); ok {

								if subnetID, ok := subnet["id"].(string); ok && len(subnetID) > 0 {

									subnetID = strings.ToLower(subnetID)

									subnetDocID := common.GenerateCombinedHashID(subnetID, common.AZURE_SUBNET_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

									assignRelatedParentResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
										subnetID, subnetDocID, common.AZURE_SUBNET_RESOURCE_TYPE, true)

									var virtualNetworkID string
									subnetSplit := strings.Split(subnetID, "/")
									for i, v := range subnetSplit {
										if v == "virtualNetworks" {
											virtualNetworkID = strings.Join(subnetSplit[:i+1], "/")
											break
										}
									}

									virtualNetworkDocID := common.GenerateCombinedHashID(virtualNetworkID, common.AZURE_VIRTUALNETWORK_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

									assignRelatedParentResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
										virtualNetworkID, virtualNetworkDocID, common.AZURE_VIRTUALNETWORK_RESOURCE_TYPE, true)
								}
							}

							if publicIpAddress, ok := ipConfigProperties["publicIPAddress"].(map[string]any); ok {

								if publicIpAddressID, ok := publicIpAddress["id"].(string); ok && len(publicIpAddressID) > 0 {

									publicIpAddressID = strings.ToLower(publicIpAddressID)

									publicIPAddressDocID := common.GenerateCombinedHashID(publicIpAddressID, common.AZURE_PUBLICIP_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

									assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
										publicIpAddressID, publicIPAddressDocID, common.AZURE_PUBLICIP_RESOURCE_TYPE, true)
								}
							}
						}
					}
				}
			}
		}

	case common.AWS_NETWORKINSIGHTSPATH_RESOURCE_TYPE:

		if networkInterfaces, ok := entityJSONMap["networkInterfaces"].([]any); ok {
			for _, networkInterface := range networkInterfaces {
				if networkInterfaceID, ok := networkInterface.(string); ok && len(networkInterfaceID) > 0 {
					networkInterfaceDocID := common.GenerateCombinedHashID(networkInterfaceID, common.NETWORKINTERFACE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						networkInterfaceID, networkInterfaceDocID, common.NETWORKINTERFACE_RESOURCE_TYPE, true)
				}
			}
		}

		if ec2Instances, ok := entityJSONMap["ec2Instances"].([]any); ok {
			for _, ec2Instance := range ec2Instances {
				if ec2InstanceID, ok := ec2Instance.(string); ok && len(ec2InstanceID) > 0 {
					ec2InstanceDocID := common.GenerateCombinedHashID(ec2InstanceID, common.AWS_EC2_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						ec2InstanceID, ec2InstanceDocID, common.AWS_EC2_RESOURCE_TYPE, true)
				}
			}
		}

		if internetGateways, ok := entityJSONMap["internetGateways"].([]any); ok {
			for _, internetGateway := range internetGateways {
				if internetGatewayID, ok := internetGateway.(string); ok && len(internetGatewayID) > 0 {
					internetGatewayDocID := common.GenerateCombinedHashID(internetGatewayID, common.AWS_INTERNETGATEWAY_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						internetGatewayID, internetGatewayDocID, common.AWS_INTERNETGATEWAY_RESOURCE_TYPE, true)
				}
			}
		}

	case common.AWS_EKSCLUSTER_RESOURCE_TYPE:

		if vpcConfig, ok := entityJSONMap["associateVPCConfig"].(map[string]any); ok {
			if subnets, ok := vpcConfig["subnetIds"].([]any); ok {
				for _, subnet := range subnets {
					if subnetID, ok := subnet.(string); ok && len(subnetID) > 0 {
						subnetDocID := common.GenerateCombinedHashID(subnetID, common.AWS_SUBNET_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

						assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
							subnetID, subnetDocID, common.AWS_SUBNET_RESOURCE_TYPE, true)
					}
				}
			}

			if secGroups, ok := vpcConfig["securityGroupIds"].([]any); ok {
				for _, secGroup := range secGroups {
					if secGroupID, ok := secGroup.(string); ok && len(secGroupID) > 0 {
						secGroupDocID := common.GenerateCombinedHashID(secGroupID, common.AWS_SG_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

						assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
							secGroupID, secGroupDocID, common.AWS_SG_RESOURCE_TYPE, true)
					}
				}
			}

			if clusterSecGroupID, ok := vpcConfig["clusterSecurityGroupId"].(string); ok && len(clusterSecGroupID) > 0 {
				clusterSecGroupDocID := common.GenerateCombinedHashID(clusterSecGroupID, common.AWS_SG_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

				assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
					clusterSecGroupID, clusterSecGroupDocID, common.AWS_SG_RESOURCE_TYPE, true)
			}

			if vpcID, ok := vpcConfig["vpcId"].(string); ok && len(vpcID) > 0 {
				vpcDocID := common.GenerateCombinedHashID(vpcID, common.AWS_VPC_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

				assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
					vpcID, vpcDocID, common.AWS_VPC_RESOURCE_TYPE, true)
			}
		}

		if encryptionConfigs, ok := entityJSONMap["encryptionConfigs"].([]any); ok {
			for _, config := range encryptionConfigs {
				if configMap, ok := config.(map[string]any); ok {
					if provider, ok := configMap["provider"].(map[string]any); ok {
						if keyAID, ok := provider["keyArn"].(string); ok && len(keyAID) > 0 {
							keyAID = strings.ToLower(keyAID)
							kmsKeyDocID := common.GenerateCombinedHashID(keyAID, common.AWS_KMS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
							assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
								keyAID, kmsKeyDocID, common.AWS_KMS_RESOURCE_TYPE, true)
						}
					}
				}
			}
		}

	case common.AWS_ELASTICSEARCH_RESOURCE_TYPE:

		if domainConfig, ok := entityJSONMap["domainConfig"].(map[string]any); ok {
			if vpcOptions, ok := domainConfig["vpcoptions"].(map[string]any); ok {
				if options, ok := vpcOptions["options"].(map[string]any); ok {
					if subnets, ok := options["subnetIds"].([]any); ok {
						for _, subnet := range subnets {
							if subnetID, ok := subnet.(string); ok && len(subnetID) > 0 {
								subnetDocID := common.GenerateCombinedHashID(subnetID, common.AWS_SUBNET_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

								assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
									subnetID, subnetDocID, common.AWS_SUBNET_RESOURCE_TYPE, true)
							}
						}
					}

					if secGroups, ok := options["securityGroupIds"].([]any); ok {
						for _, secGroup := range secGroups {
							if secGroupID, ok := secGroup.(string); ok && len(secGroupID) > 0 {
								secGroupDocID := common.GenerateCombinedHashID(secGroupID, common.AWS_SG_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

								assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
									secGroupID, secGroupDocID, common.AWS_SG_RESOURCE_TYPE, true)
							}
						}
					}

					if vpcID, ok := options["vpcid"].(string); ok && len(vpcID) > 0 {
						vpcDocID := common.GenerateCombinedHashID(vpcID, common.AWS_VPC_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

						assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
							vpcID, vpcDocID, common.AWS_VPC_RESOURCE_TYPE, true)
					}

				}
			}
		}

	case common.AWS_AICOMPREHENDDOCCLASSIFIER_RESOURCE_TYPE, common.AWS_SAGEMAKERLABELINGJOBS_RESOURCE_TYPE:

		if s3Buckets, ok := entityJSONMap["s3BucketNames"].([]any); ok {
			for _, s3Bucket := range s3Buckets {
				if s3BucketID, ok := s3Bucket.(string); ok && len(s3BucketID) > 0 {

					// Bucket can be from any account
					s3BucketDocID, err := common.GetCloudResourceDocumentForEntityIDAndType(s3BucketID, common.AWS_S3_RESOURCE_TYPE, resourceContextDoc.TenantID, resourceContext.LastCollectedAt)
					if err != nil {
						break
					}

					if accountID, ok := s3BucketDocID["accountId"].(string); ok && len(accountID) > 0 {
						bucketDocID := common.GenerateCombinedHashID(s3BucketID, common.AWS_S3_RESOURCE_TYPE, accountID, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
						assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
							s3BucketID, bucketDocID, common.AWS_S3_RESOURCE_TYPE, true)
					}
				}
			}
		}

	case common.AWS_SAGEMAKERARTIFACT_RESOURCE_TYPE:

		if s3BucketID, ok := entityJSONMap["bucketName"].(string); ok && len(s3BucketID) > 0 {

			// Bucket can be from any account
			s3BucketDocID, err := common.GetCloudResourceDocumentForEntityIDAndType(s3BucketID, common.AWS_S3_RESOURCE_TYPE, resourceContextDoc.TenantID, resourceContext.LastCollectedAt)
			if err != nil {
				break
			}

			if accountID, ok := s3BucketDocID["accountId"].(string); ok && len(accountID) > 0 {
				bucketDocID := common.GenerateCombinedHashID(s3BucketID, common.AWS_S3_RESOURCE_TYPE, accountID, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
					s3BucketID, bucketDocID, common.AWS_S3_RESOURCE_TYPE, true)
			}
		}

	case common.AWS_BEDROCKAGENTGROUP_RESOURCE_TYPE:

		if bedrockAgent, ok := entityJSONMap["agentId"].(string); ok && len(bedrockAgent) > 0 {
			bedrockAgentID := strings.ToLower(bedrockAgent)

			bedrockAgentDocID := common.GenerateCombinedHashID(bedrockAgentID, common.AWS_BEDROCKAGENT_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

			assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				bedrockAgentID, bedrockAgentDocID, common.AWS_BEDROCKAGENT_RESOURCE_TYPE, true)
		}

	case common.AWS_SSMINSTANCE_RESOURCE_TYPE:

		if instanceID, ok := entityJSONMap["sourceId"].(string); ok {

			instanceDocID := common.GenerateCombinedHashID(instanceID, common.AWS_EC2_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

			assignRelatedParentResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				instanceID, instanceDocID, common.AWS_EC2_RESOURCE_TYPE, true)
		}

	case common.AWS_GLUECATALOGTABLE_RESOURCE_TYPE:

		if databaseName, ok := entityJSONMap["databaseName"].(string); ok {
			databaseDocID := common.GenerateCombinedHashID(databaseName, common.AWS_GLUECATALOGDATABASE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

			assignRelatedParentResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				databaseName, databaseDocID, common.AWS_GLUECATALOGDATABASE_RESOURCE_TYPE, true)
		}

		if s3buckets, ok := entityJSONMap["s3BucketNames"].([]any); ok {

			for _, s3bucket := range s3buckets {
				if s3bucket, ok := s3bucket.(string); ok {
					s3bucket = strings.ToLower(s3bucket)
					// Bucket can be from any account
					s3Doc, err := common.GetCloudResourceDocumentForEntityIDAndType(s3bucket, common.AWS_S3_RESOURCE_TYPE, resourceContextDoc.TenantID, resourceContext.LastCollectedAt)
					if err != nil {
						break
					}

					if accountID, ok := s3Doc["accountId"].(string); ok && len(accountID) > 0 {
						bucketDocID := common.GenerateCombinedHashID(s3bucket, common.AWS_S3_RESOURCE_TYPE, accountID, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
						assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
							s3bucket, bucketDocID, common.AWS_S3_RESOURCE_TYPE, true)
					}
				}
			}
		}

		if createdByRole, ok := entityJSONMap["createdBy"].(string); ok {

			splitArn := strings.Split(createdByRole, "/")
			role := strings.ToLower(splitArn[len(splitArn)-1])

			roleDocID := common.GenerateCombinedHashID(role, common.AWS_IAM_ROLE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				role, roleDocID, common.AWS_IAM_ROLE_RESOURCE_TYPE, true)
		}

	case common.AWS_GUARDDUTYFINDING_RESOURCE_TYPE:
		if gDFinding, ok := entityJSONMap["finding"].(map[string]any); ok {
			if resource, ok := gDFinding["resource"].(map[string]any); ok {
				if instanceDetails, ok := resource["instanceDetails"].(map[string]any); ok {

					if instanceID, ok := instanceDetails["instanceId"].(string); ok && len(instanceID) > 0 {

						instanceID = strings.ToLower(instanceID)
						instanceDocID := common.GenerateCombinedHashID(instanceID, common.AWS_EC2_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
						assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
							instanceID, instanceDocID, common.AWS_EC2_RESOURCE_TYPE, true)
					}

					if amiID, ok := instanceDetails["imageId"].(string); ok && len(amiID) >= 0 {

						amiID = strings.ToLower(amiID)
						amiDocID := common.GenerateCombinedHashID(amiID, common.AWS_AMI_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

						assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
							amiID, amiDocID, common.AWS_AMI_RESOURCE_TYPE, true)
					}

					if networkInterfaces, ok := instanceDetails["networkInterfaces"].([]any); ok {
						for _, networkInterface := range networkInterfaces {

							if networkInterfaceMap, ok := networkInterface.(map[string]any); ok {
								if networkInterfaceID, ok := networkInterfaceMap["networkInterfaceId"].(string); ok && len(networkInterfaceID) > 0 {

									networkInterfaceID = strings.ToLower(networkInterfaceID)
									networkInterfaceDocID := common.GenerateCombinedHashID(networkInterfaceID, common.NETWORKINTERFACE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
									assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
										networkInterfaceID, networkInterfaceDocID, common.NETWORKINTERFACE_RESOURCE_TYPE, true)
								}

								if securityGroups, ok := networkInterfaceMap["securityGroups"].([]any); ok {
									for _, securityGroup := range securityGroups {
										if sgMap, ok := securityGroup.(map[string]any); ok {
											if sgID, ok := sgMap["groupId"].(string); ok && len(sgID) > 0 {

												sgID = strings.ToLower(sgID)
												sgDocID := common.GenerateCombinedHashID(sgID, common.AWS_SG_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
												assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
													sgID, sgDocID, common.AWS_SG_RESOURCE_TYPE, true)
											}
										}
									}
								}

								if subnetID, ok := networkInterfaceMap["subnetId"].(string); ok && len(subnetID) > 0 {

									subnetID = strings.ToLower(subnetID)
									subnetDocID := common.GenerateCombinedHashID(subnetID, common.AWS_SUBNET_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
									assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
										subnetID, subnetDocID, common.AWS_SUBNET_RESOURCE_TYPE, true)
								}

								if vpcID, ok := networkInterfaceMap["vpcId"].(string); ok && len(vpcID) > 0 {

									vpcID = strings.ToLower(vpcID)
									vpcDocID := common.GenerateCombinedHashID(vpcID, common.AWS_VPC_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
									assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
										vpcID, vpcDocID, common.AWS_VPC_RESOURCE_TYPE, true)
								}
							}
						}

					}
				}
			}
		}

	case common.AWS_AISAGEMAKERDOMAIN_RESOURCE_TYPE:
		if vpcID, ok := entityJSONMap["vpcId"].(string); ok && len(vpcID) > 0 {

			vpcID = strings.ToLower(vpcID)
			vpcDocID := common.GenerateCombinedHashID(vpcID, common.AWS_VPC_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				vpcID, vpcDocID, common.AWS_VPC_RESOURCE_TYPE, true)
		}

		if subnetIds, ok := entityJSONMap["subnetIds"].([]any); ok {
			for _, subnetID := range subnetIds {
				if subnetID, ok := subnetID.(string); ok && len(subnetID) > 0 {

					subnetID = strings.ToLower(subnetID)
					subnetDocID := common.GenerateCombinedHashID(subnetID, common.AWS_SUBNET_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						subnetID, subnetDocID, common.AWS_SUBNET_RESOURCE_TYPE, true)
				}
			}
		}

	case common.AWS_ELB_RESOURCE_TYPE:
		if ec2Instances, ok := entityJSONMap["ec2Instances"].([]any); ok {
			for _, ec2Instance := range ec2Instances {
				if ec2InstanceID, ok := ec2Instance.(string); ok && len(ec2InstanceID) > 0 {

					ec2InstanceID = strings.ToLower(ec2InstanceID)
					ec2InstanceDocID := common.GenerateCombinedHashID(ec2InstanceID, common.AWS_EC2_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						ec2InstanceID, ec2InstanceDocID, common.AWS_EC2_RESOURCE_TYPE, true)
				}
			}
		}

		if vpcIds, ok := entityJSONMap["vpcIds"].([]any); ok {
			for _, vpcID := range vpcIds {
				if vpcID, ok := vpcID.(string); ok && len(vpcID) > 0 {

					vpcID = strings.ToLower(vpcID)
					vpcDocID := common.GenerateCombinedHashID(vpcID, common.AWS_VPC_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						vpcID, vpcDocID, common.AWS_VPC_RESOURCE_TYPE, true)
				}
			}
		}

		if subnetIds, ok := entityJSONMap["subnets"].([]any); ok {
			for _, subnetID := range subnetIds {
				if subnetID, ok := subnetID.(string); ok && len(subnetID) > 0 {

					subnetID = strings.ToLower(subnetID)
					subnetDocID := common.GenerateCombinedHashID(subnetID, common.AWS_SUBNET_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						subnetID, subnetDocID, common.AWS_SUBNET_RESOURCE_TYPE, true)
				}
			}
		}

		if securityGroups, ok := entityJSONMap["securityGroups"].([]any); ok {
			for _, securityGroup := range securityGroups {
				if sgID, ok := securityGroup.(string); ok && len(sgID) > 0 {

					sgID = strings.ToLower(sgID)
					sgDocID := common.GenerateCombinedHashID(sgID, common.AWS_SG_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						sgID, sgDocID, common.AWS_SG_RESOURCE_TYPE, true)
				}
			}
		}

	case common.AWS_EBSVOLUME_RESOURCE_TYPE:

		if clusterArn, ok := entityJSONMap["clusterArn"].(string); ok && len(clusterArn) >= 0 {

			cluster := strings.ToLower(clusterArn)
			clusterDocID := common.GenerateCombinedHashID(cluster, common.AWS_EKSCLUSTER_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				cluster, clusterDocID, common.AWS_EKSCLUSTER_RESOURCE_TYPE, true)
		}

		if kmsKeyID, ok := entityJSONMap["kmsKeyId"].(string); ok && len(kmsKeyID) > 0 {
			kmsKeyID = strings.ToLower(kmsKeyID)
			kmsKeyDocID := common.GenerateCombinedHashID(kmsKeyID, common.AWS_KMS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

			assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				kmsKeyID, kmsKeyDocID, common.AWS_KMS_RESOURCE_TYPE, true)
		}

	case common.AWS_ECSCLUSTER_RESOURCE_TYPE:

		if instances, ok := entityJSONMap["ec2InstanceIds"].([]any); ok {
			for _, instance := range instances {
				if instanceID, ok := instance.(string); ok && len(instanceID) > 0 {
					instanceID = strings.ToLower(instanceID)
					instanceDocID := common.GenerateCombinedHashID(instanceID, common.AWS_EC2_RESOURCE_TYPE, resourceContextDoc.Account, resourceContextDoc.TenantID)
					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						instanceID, instanceDocID, common.AWS_EC2_RESOURCE_TYPE, true)
				}
			}
		}

	case common.GCP_INSTANCE_RESOURCE_TYPE:

		if vmInstance, ok := entityJSONMap["vmInstance"].(map[string]any); ok {

			if vmDisks, ok := vmInstance["disks"].([]any); ok {

				for _, vmDisk := range vmDisks {

					if vmDiskMap, ok := vmDisk.(map[string]any); ok {

						if diskSource, ok := vmDiskMap["source"].(string); ok && len(diskSource) > 0 {

							var vmDiskID string

							splitName := strings.Split(diskSource, "/")
							for i, v := range splitName {
								if v == "projects" {
									vmDiskID = strings.Join(splitName[i:], "/")
									break
								}
							}

							vmDiskDocID := common.GenerateCombinedHashID(vmDiskID, common.GCP_VMDISK_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

							assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
								vmDiskID, vmDiskDocID, common.GCP_VMDISK_RESOURCE_TYPE, true)
						}
					}
				}
			}

			if nwInterfaces, ok := vmInstance["networkInterfaces"].([]any); ok {

				for _, nwInterface := range nwInterfaces {

					if nwInterfaceMap, ok := nwInterface.(map[string]any); ok {

						if network, ok := nwInterfaceMap["network"].(string); ok && len(network) > 0 {

							var networkID string

							splitName := strings.Split(network, "/")
							for i, v := range splitName {
								if v == "projects" {
									networkID = strings.Join(splitName[i:], "/")
									break
								}
							}

							networkDocID := common.GenerateCombinedHashID(networkID, common.GCP_NETWORK_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

							assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
								networkID, networkDocID, common.GCP_NETWORK_RESOURCE_TYPE, true)

							resourceContext.RangeGCPFirewallTargets(func(network string, targets map[string][]string) bool {
								if allTargets, ok := targets["ALL"]; ok {
									for _, firewallRuleID := range allTargets {
										firewallRuleDocID := common.GenerateCombinedHashID(
											firewallRuleID,
											common.GCP_FIREWALL_RESOURCE_TYPE,
											resourceContextDoc.Account,
											resourceContextDoc.TenantID,
										)

										assignRelatedResource(
											resourceContext,
											resourceContextDoc.ResourceID,
											resourceDocID,
											resourceContextDoc.ResourceType,
											firewallRuleID,
											firewallRuleDocID,
											common.GCP_FIREWALL_RESOURCE_TYPE,
											true,
										)
									}
								}
								return true
							})

							if networkTags, ok := vmInstance["tags"].(map[string]any); ok {
								if tagItems, ok := networkTags["items"].([]any); ok {
									for _, tagItem := range tagItems {
										if networkTag, ok := tagItem.(string); ok && len(networkTag) > 0 {
											processFirewallRulesForTarget(resourceContext, network, "Tag:"+networkTag,
												resourceContextDoc, resourceDocID)
										}
									}
								}
							}

							if vmServiceAccounts, ok := vmInstance["serviceAccounts"].([]any); ok {
								for _, vmServiceAccount := range vmServiceAccounts {
									if vmServiceAccountMap, ok := vmServiceAccount.(map[string]any); ok {
										if serviceAccountEmail, ok := vmServiceAccountMap["email"].(string); ok && len(serviceAccountEmail) > 0 {
											processFirewallRulesForTarget(resourceContext, network, "SA:"+serviceAccountEmail,
												resourceContextDoc, resourceDocID)
										}
									}
								}
							}
						}

						if subNetwork, ok := nwInterfaceMap["subnetwork"].(string); ok && len(subNetwork) > 0 {

							var subNetworkID string

							splitName := strings.Split(subNetwork, "/")
							for i, v := range splitName {
								if v == "projects" {
									subNetworkID = strings.Join(splitName[i:], "/")
									break
								}
							}

							subNetworkDocID := common.GenerateCombinedHashID(subNetworkID, common.GCP_SUBNETWORK_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

							assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
								subNetworkID, subNetworkDocID, common.GCP_SUBNETWORK_RESOURCE_TYPE, true)
						}
					}
				}
			}
		}

		if serviceAccount, ok := entityJSONMap["vmServiceAccountEmail"].(string); ok && len(serviceAccount) > 0 {

			serviceAccount = strings.ToLower(serviceAccount)
			serviceAccountDocID := common.GenerateCombinedHashID(serviceAccount, common.GCP_SERVICEACCOUNT_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

			assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				serviceAccount, serviceAccountDocID, common.GCP_SERVICEACCOUNT_RESOURCE_TYPE, true)
		}

		if portInfo, ok := entityJSONMap["portAdditionalInfo"].(map[string]any); ok {

			if ipDetails, ok := portInfo["ipAddress"].(string); ok && len(ipDetails) > 0 {

				ipDetailsDocID := common.GenerateCombinedHashID(ipDetails, common.GCP_IPDETAILS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

				assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
					ipDetails, ipDetailsDocID, common.GCP_IPDETAILS_RESOURCE_TYPE, true)
			}
		}

		if clusterID, ok := entityJSONMap["clusterName"].(string); ok && len(clusterID) > 0 {

			clusterID = strings.ToLower(clusterID)
			clusterDocID := common.GenerateCombinedHashID(clusterID, common.GCP_GKECLUSTER_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

			assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				clusterID, clusterDocID, common.GCP_GKECLUSTER_RESOURCE_TYPE, true)
		}

		if instance, ok := entityJSONMap["vmInstance"].(map[string]any); ok {
			if encryptionKey, ok := instance["instanceEncryptionKey"].(map[string]any); ok {
				if kmsKey, ok := encryptionKey["kmsKeyName"].(string); ok && len(kmsKey) > 0 {
					kmsKey = strings.ToLower(kmsKey)
					kmsDocID := common.GenerateCombinedHashID(kmsKey, common.GCP_KMS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, kmsKey, kmsDocID, common.GCP_KMS_RESOURCE_TYPE, true)
				}
			}
		}

	case common.GCP_NETWORK_RESOURCE_TYPE:

		if network, ok := entityJSONMap["network"].(map[string]any); ok {
			if subNetworks, ok := network["subnetworks"].([]any); ok {
				for _, subNetwork := range subNetworks {

					if subNetworkString, ok := subNetwork.(string); ok {

						var subNetworkID string

						splitName := strings.Split(subNetworkString, "/")
						for i, v := range splitName {
							if v == "projects" {
								subNetworkID = strings.Join(splitName[i:], "/")
								break
							}
						}

						subNetworkID = strings.ToLower(subNetworkID)
						subNetworkDocID := common.GenerateCombinedHashID(subNetworkID, common.GCP_SUBNETWORK_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

						assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
							subNetworkID, subNetworkDocID, common.GCP_SUBNETWORK_RESOURCE_TYPE, true)
					}
				}
			}
		}

	case common.GCP_SERVICEACCOUNTKEY_RESOURCE_TYPE:

		if serviceAccount, ok := entityJSONMap["serviceAccount"].(string); ok {

			serviceAccount = strings.ToLower(serviceAccount)
			serviceAccountDocID := common.GenerateCombinedHashID(serviceAccount, common.GCP_SERVICEACCOUNT_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

			assignRelatedParentResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				serviceAccount, serviceAccountDocID, common.GCP_SERVICEACCOUNT_RESOURCE_TYPE, true)
		}

	case common.GCP_VMDISK_RESOURCE_TYPE:
		if disk, ok := entityJSONMap["disk"].(map[string]any); ok {
			if encryptionKey, ok := disk["diskEncryptionKey"].(map[string]any); ok {
				if kmsKey, ok := encryptionKey["kmsKeyName"].(string); ok && len(kmsKey) > 0 {
					kmsKey = strings.ToLower(kmsKey)
					kmsDocID := common.GenerateCombinedHashID(kmsKey, common.GCP_KMS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, kmsKey, kmsDocID, common.GCP_KMS_RESOURCE_TYPE, true)
				}
			}
		}

	case common.GCP_DISK_SNAPSHOT_RESOURCE_TYPE:

		if sourceDisk, ok := entityJSONMap["sourceDisk"].(string); ok && len(sourceDisk) >= 0 {

			var vmDiskID string

			splitName := strings.Split(sourceDisk, "/")
			for i, v := range splitName {
				if v == "projects" {
					vmDiskID = strings.Join(splitName[i:], "/")
					break
				}
			}

			vmDiskDocID := common.GenerateCombinedHashID(vmDiskID, common.GCP_VMDISK_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

			assignRelatedParentResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				vmDiskID, vmDiskDocID, common.GCP_VMDISK_RESOURCE_TYPE, true)
		}

	case common.GCP_AUTOSCALER_RESOURCE_TYPE:

		if autoscaler, ok := entityJSONMap["autoscaler"].(map[string]any); ok {

			if target, ok := autoscaler["target"].(string); ok && len(target) >= 0 {

				var instanceGroupManagerID string

				splitName := strings.Split(target, "/")
				for i, v := range splitName {
					if v == "projects" {
						instanceGroupManagerID = strings.Join(splitName[i:], "/")
						break
					}
				}

				instanceGroupManagerID = strings.ToLower(instanceGroupManagerID)

				instanceGroupManagerDocID := common.GenerateCombinedHashID(instanceGroupManagerID, common.GCP_INSTANCEGROUPMANAGER_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

				assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
					instanceGroupManagerID, instanceGroupManagerDocID, common.GCP_INSTANCEGROUPMANAGER_RESOURCE_TYPE, true)
			}
		}

	case common.GCP_INSTANCEGROUPMANAGER_RESOURCE_TYPE:

		if instanceGroupManager, ok := entityJSONMap["instanceGroupManager"].(map[string]any); ok {

			if instanceGroup, ok := instanceGroupManager["instanceGroup"].(string); ok && len(instanceGroup) >= 0 {

				var instanceGroupID string

				splitName := strings.Split(instanceGroup, "/")
				for i, v := range splitName {
					if v == "projects" {
						instanceGroupID = strings.Join(splitName[i:], "/")
						break
					}
				}

				instanceGroupID = strings.ToLower(instanceGroupID)

				instanceGroupDocID := common.GenerateCombinedHashID(instanceGroupID, common.GCP_INSTANCEGROUP_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

				assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
					instanceGroupID, instanceGroupDocID, common.GCP_INSTANCEGROUP_RESOURCE_TYPE, true)
			}
		}

		if instanceTemplate, ok := entityJSONMap["instanceTemplate"].(string); ok && len(instanceTemplate) > 0 {
			instanceTemplate = strings.ToLower(instanceTemplate)
			instanceTemplateDocID := common.GenerateCombinedHashID(instanceTemplate, common.GCP_CONTAINERINSTANCETEMPLATE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			assignRelatedResourceWithTransitive(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				instanceTemplate, instanceTemplateDocID, common.GCP_CONTAINERINSTANCETEMPLATE_RESOURCE_TYPE, common.GCP_GKECLUSTER_RESOURCE_TYPE, true)
		}

	case common.GCP_INSTANCEGROUP_RESOURCE_TYPE:

		if groupInstances, ok := entityJSONMap["instanceWithNamedPortsList"].([]any); ok {

			for _, groupInstance := range groupInstances {

				if instanceMap, ok := groupInstance.(map[string]any); ok {

					if instance, ok := instanceMap["instance"].(string); ok && len(instance) > 0 {

						var instanceID string

						splitName := strings.Split(instance, "/")
						for i, v := range splitName {
							if v == "projects" {
								instanceID = strings.Join(splitName[i:], "/")
								break
							}
						}

						instanceDocID := common.GenerateCombinedHashID(instanceID, common.GCP_INSTANCE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

						assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
							instanceID, instanceDocID, common.GCP_INSTANCE_RESOURCE_TYPE, true)
					}
				}
			}
		}

	case common.GCP_CLOUDSTORAGE_RESOURCE_TYPE:
		if bucket, ok := entityJSONMap["bucket"].(map[string]any); ok {
			if encryption, ok := bucket["encryption"].(map[string]any); ok {
				if kmsKey, ok := encryption["defaultKmsKeyName"].(string); ok && len(kmsKey) > 0 {
					kmsKey = strings.ToLower(kmsKey)
					kmsDocID := common.GenerateCombinedHashID(kmsKey, common.GCP_KMS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, kmsKey, kmsDocID, common.GCP_KMS_RESOURCE_TYPE, true)
				}
			}
		}

	case common.LOADBALANCER_RESOURCE_TYPE:

		// GCP

		if urlMap, ok := entityJSONMap["urlMap"].(map[string]any); ok {

			if defaultService, ok := urlMap["defaultService"].(string); ok && len(defaultService) >= 0 {

				var backendServiceID string

				splitName := strings.Split(defaultService, "/")
				for i, v := range splitName {
					if v == "projects" {
						backendServiceID = strings.Join(splitName[i:], "/")
						break
					}
				}

				backendServiceDocID := common.GenerateCombinedHashID(backendServiceID, common.GCP_BACKENDSERVICE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

				assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
					backendServiceID, backendServiceDocID, common.GCP_BACKENDSERVICE_RESOURCE_TYPE, true)
			}
		}

		// AZURE

		if properties, ok := entityJSONMap["properties"].(map[string]any); ok {

			if frontendIPConfigurations, ok := properties["frontendIPConfigurations"].([]any); ok {

				for _, ipConfig := range frontendIPConfigurations {

					if ipConfigMap, ok := ipConfig.(map[string]any); ok {

						if ipConfigProperties, ok := ipConfigMap["properties"].(map[string]any); ok {

							if publicIPAddress, ok := ipConfigProperties["publicIPAddress"].(map[string]any); ok {

								if publicIpAddressID, ok := publicIPAddress["id"].(string); ok && len(publicIpAddressID) > 0 {

									publicIpAddressID = strings.ToLower(publicIpAddressID)

									publicIPAddressDocID := common.GenerateCombinedHashID(publicIpAddressID, common.AZURE_PUBLICIP_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

									assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
										publicIpAddressID, publicIPAddressDocID, common.AZURE_PUBLICIP_RESOURCE_TYPE, true)
								}
							}
						}
					}
				}
			}
		}

		if subnetIDs, ok := entityJSONMap["subnetIds"].([]any); ok {
			for _, subnetID := range subnetIDs {

				if subnetIDString, ok := subnetID.(string); ok && len(subnetIDString) > 0 {

					subnetIDString = strings.ToLower(subnetIDString)

					subnetDocID := common.GenerateCombinedHashID(subnetIDString, common.AZURE_SUBNET_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						subnetIDString, subnetDocID, common.AZURE_SUBNET_RESOURCE_TYPE, true)
				}
			}
		}

		var clusterName, clusterRgName string

		if tags, ok := entityJSONMap["tags"].([]any); ok {
			for _, tag := range tags {

				if tagMap, ok := tag.(map[string]any); ok {

					if tagKey, ok := tagMap["key"].(string); ok && len(tagKey) > 0 {
						if tagValue, ok := tagMap["value"].(string); ok && len(tagValue) > 0 {

							switch tagKey {
							case "aks-managed-cluster-name":
								clusterName = tagValue
							case "aks-managed-cluster-rg":
								clusterRgName = tagValue
							}
						}
					}
				}
			}
		}

		if len(clusterName) > 0 && len(clusterRgName) > 0 {

			clusterID := "/subscriptions/" + resourceContextDoc.Account + "/resourcegroups/" + clusterRgName + "/providers/microsoft.containerservice/managedclusters/" + clusterName

			clusterID = strings.ToLower(clusterID)

			clusterDocID := common.GenerateCombinedHashID(clusterID, common.AZURE_AKSCLUSTER_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

			assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				clusterID, clusterDocID, common.AZURE_AKSCLUSTER_RESOURCE_TYPE, true)
		}

	case common.GCP_BACKENDSERVICE_RESOURCE_TYPE:

		if igs, ok := entityJSONMap["instanceGroups"].([]any); ok {
			for _, ig := range igs {
				if igString, ok := ig.(string); ok && len(igString) >= 0 {

					igString = strings.ToLower(igString)

					if strings.Contains(igString, "/networkendpointgroups/") {
						networkEndpointGroupID := igString
						networkEndpointGroupDocID := common.GenerateCombinedHashID(networkEndpointGroupID, common.GCP_NETWORKENDPOINTGROUP_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
						assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
							networkEndpointGroupID, networkEndpointGroupDocID, common.GCP_NETWORKENDPOINTGROUP_RESOURCE_TYPE, true)
					} else if strings.Contains(igString, "/instancegroups/") {
						instanceGroupID := igString
						instanceGroupDocID := common.GenerateCombinedHashID(instanceGroupID, common.GCP_INSTANCEGROUP_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
						assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
							instanceGroupID, instanceGroupDocID, common.GCP_INSTANCEGROUP_RESOURCE_TYPE, true)
					}
				}
			}
		}

		if loadBalancers, ok := entityJSONMap["loadBalancers"].([]any); ok {
			for _, loadBalancer := range loadBalancers {
				if loadBalancerID, ok := loadBalancer.(string); ok && len(loadBalancerID) >= 0 {
					loadBalancerID = strings.ToLower(loadBalancerID)
					loadBalancerDocID := common.GenerateCombinedHashID(loadBalancerID, common.LOADBALANCER_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						loadBalancerID, loadBalancerDocID, common.LOADBALANCER_RESOURCE_TYPE, true)
				}
			}
		}

		if healthChecks, ok := entityJSONMap["healthChecks"].([]any); ok {
			for _, healthCheck := range healthChecks {
				if computeHealthCheckID, ok := healthCheck.(string); ok && len(computeHealthCheckID) > 0 {
					computeHealthCheckID = strings.ToLower(computeHealthCheckID)
					computeHealthCheckDocID := common.GenerateCombinedHashID(computeHealthCheckID, common.GCP_COMPUTEHEALTHCHECK_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						computeHealthCheckID, computeHealthCheckDocID, common.GCP_COMPUTEHEALTHCHECK_RESOURCE_TYPE, true)
				}
			}
		}

	case common.GCP_FUNCTION_RESOURCE_TYPE:
		if fn, ok := entityJSONMap["function"].(map[string]any); ok {
			if src, ok := fn["source"].(map[string]any); ok {
				if storageSource, ok := src["storageSource"].(map[string]any); ok {
					if bucket, ok := storageSource["bucket"].(string); ok && len(bucket) > 0 {
						bucket = strings.ToLower(bucket)
						bucketDocID := common.GenerateCombinedHashID(bucket, common.GCP_CLOUDSTORAGE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
						assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, bucket, bucketDocID, common.GCP_CLOUDSTORAGE_RESOURCE_TYPE, true)
					}
				}
			}
		}

	case common.GCP_DOCKERIMAGE_RESOURCE_TYPE:

		var repositoryID string

		splitName := strings.Split(resourceContextDoc.ResourceID, "/")
		for i, v := range splitName {
			if v == "dockerimages" {
				repositoryID = strings.Join(splitName[:i], "/")
				break
			}
		}

		repositoryDocID := common.GenerateCombinedHashID(repositoryID, common.GCP_ARTIFACTREPOSITORY_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

		assignRelatedParentResourceOneWay(resourceContext, resourceDocID, repositoryID, repositoryDocID,
			common.GCP_ARTIFACTREPOSITORY_RESOURCE_TYPE, true)

	case common.GCP_VERTEXAIENDPOINT_RESOURCE_TYPE:

		if deployedModels, ok := entityJSONMap["deployedModels"].([]any); ok {
			for _, deployedModel := range deployedModels {
				if vertexModel, ok := deployedModel.(map[string]any); ok {
					if vertextModelID, ok := vertexModel["model"].(string); ok && len(vertextModelID) > 0 {
						vertextModelID = strings.ToLower(vertextModelID)
						vertextModelDocID := common.GenerateCombinedHashID(vertextModelID, common.GCP_VERTEXAIMODEL_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
						assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
							vertextModelID, vertextModelDocID, common.GCP_VERTEXAIMODEL_RESOURCE_TYPE, true)
					}
				}
			}
		}

		if networkID, ok := entityJSONMap["network"].(string); ok && len(networkID) > 0 {
			networkID = strings.ToLower(networkID)
			networkDocID := common.GenerateCombinedHashID(networkID, common.GCP_NETWORK_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				networkID, networkDocID, common.GCP_NETWORK_RESOURCE_TYPE, true)
		}

	case common.GCP_DIALOGFLOWAGENT_RESOURCE_TYPE:

		if agentTools, ok := entityJSONMap["tools"].([]any); ok {
			for _, agentTool := range agentTools {
				if agentToolMap, ok := agentTool.(map[string]any); ok {
					if dataStoreSpec, ok := agentToolMap["dataStoreSpec"].(map[string]any); ok {
						if dataStoreConnections, ok := dataStoreSpec["dataStoreConnections"].([]any); ok {
							for _, dataStoreConnection := range dataStoreConnections {
								if dataStoreConnectionMap, ok := dataStoreConnection.(map[string]any); ok {
									if dataStoreID, ok := dataStoreConnectionMap["dataStore"].(string); ok && len(dataStoreID) > 0 {
										dataStoreID = strings.ToLower(dataStoreID)
										dataStoreDocID := common.GenerateCombinedHashID(dataStoreID, common.GCP_DISCOVERYENGINEDATASTORE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
										assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
											dataStoreID, dataStoreDocID, common.GCP_DISCOVERYENGINEDATASTORE_RESOURCE_TYPE, true)
									}
								}
							}
						}
					}
				}
			}
		}

		if dialogFlowPlaybookIDs, ok := entityJSONMap["playbookIds"].([]any); ok {
			for _, dialogFlowPlaybookID := range dialogFlowPlaybookIDs {
				if dialogFlowPlaybook, ok := dialogFlowPlaybookID.(string); ok && len(dialogFlowPlaybook) > 0 {
					dialogFlowPlaybook = strings.ToLower(dialogFlowPlaybook)
					dialogFlowPlaybookDocID := common.GenerateCombinedHashID(dialogFlowPlaybook, common.GCP_DIALOGFLOWAGENTPLAYBOOK_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						dialogFlowPlaybook, dialogFlowPlaybookDocID, common.GCP_DIALOGFLOWAGENTPLAYBOOK_RESOURCE_TYPE, true)
				}
			}
		}

		if dialogFlowToolIDs, ok := entityJSONMap["toolIds"].([]any); ok {
			for _, dialogFlowToolID := range dialogFlowToolIDs {
				if dialogFlowTool, ok := dialogFlowToolID.(string); ok && len(dialogFlowTool) > 0 {
					dialogFlowTool = strings.ToLower(dialogFlowTool)
					dialogFlowToolDocID := common.GenerateCombinedHashID(dialogFlowTool, common.GCP_DIALOGFLOWAGENTTOOL_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						dialogFlowTool, dialogFlowToolDocID, common.GCP_DIALOGFLOWAGENTTOOL_RESOURCE_TYPE, true)
				}
			}
		}

		if dialogFlowWebookIDs, ok := entityJSONMap["webhookIds"].([]any); ok {
			for _, dialogFlowWebookID := range dialogFlowWebookIDs {
				if dialogFlowWebook, ok := dialogFlowWebookID.(string); ok && len(dialogFlowWebook) > 0 {
					dialogFlowWebook = strings.ToLower(dialogFlowWebook)
					dialogFlowWebookDocID := common.GenerateCombinedHashID(dialogFlowWebook, common.GCP_DIALOGFLOWAGENTWEBHOOK_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						dialogFlowWebook, dialogFlowWebookDocID, common.GCP_DIALOGFLOWAGENTWEBHOOK_RESOURCE_TYPE, true)
				}
			}
		}

		if dialogFlowSecSettings, ok := entityJSONMap["securitySettings"].(string); ok && len(dialogFlowSecSettings) > 0 {
			dialogFlowSecSettings = strings.ToLower(dialogFlowSecSettings)
			dialogFlowSecSettingsDocID := common.GenerateCombinedHashID(dialogFlowSecSettings, common.GCP_DIALOGFLOWAGENTSECSETTINGS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				dialogFlowSecSettings, dialogFlowSecSettingsDocID, common.GCP_DIALOGFLOWAGENTSECSETTINGS_RESOURCE_TYPE, true)
		}

	case common.GCP_DIALOGFLOWAGENTTOOL_RESOURCE_TYPE:

		if discoveryEngineDataStoreIDs, ok := entityJSONMap["dataStoreIds"].([]any); ok {
			for _, discoveryEngineDataStoreID := range discoveryEngineDataStoreIDs {
				if discoveryEngineDataStore, ok := discoveryEngineDataStoreID.(string); ok && len(discoveryEngineDataStore) > 0 {
					discoveryEngineDataStore = strings.ToLower(discoveryEngineDataStore)
					discoveryEngineDataStoreDocID := common.GenerateCombinedHashID(discoveryEngineDataStore, common.GCP_DISCOVERYENGINEDATASTORE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						discoveryEngineDataStore, discoveryEngineDataStoreDocID, common.GCP_DISCOVERYENGINEDATASTORE_RESOURCE_TYPE, true)
				}
			}
		}

	case common.GCP_DISCOVERYENGINEDATASTORE_RESOURCE_TYPE:

		if buckets, ok := entityJSONMap["cloudStorageIds"].([]any); ok {
			for _, bucket := range buckets {
				if bucketID, ok := bucket.(string); ok && len(bucketID) > 0 {
					bucketID = strings.ToLower(bucketID)

					// Bucket can be from any account
					cloudStorage, err := common.GetCloudResourceDocumentForEntityIDAndType(bucketID, common.GCP_CLOUDSTORAGE_RESOURCE_TYPE, resourceContextDoc.TenantID, resourceContext.LastCollectedAt)
					if err != nil {
						break
					}

					if accountID, ok := cloudStorage["accountId"].(string); ok && len(accountID) > 0 {
						bucketDocID := common.GenerateCombinedHashID(bucketID, common.GCP_CLOUDSTORAGE_RESOURCE_TYPE, accountID, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
						assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
							bucketID, bucketDocID, common.GCP_CLOUDSTORAGE_RESOURCE_TYPE, true)
					}
				}
			}
		}

	case common.GCP_BIGQUERYTABLE_RESOURCE_TYPE:

		if bigQueryDataSetID, ok := entityJSONMap["bigQueryDatasetId"].(string); ok && len(bigQueryDataSetID) > 0 {

			bigQueryDataSetID = strings.ToLower(bigQueryDataSetID)

			bigQueryDatasetDocID := common.GenerateCombinedHashID(bigQueryDataSetID, common.GCP_BIGQUERYDATASET_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

			assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				bigQueryDataSetID, bigQueryDatasetDocID, common.GCP_BIGQUERYDATASET_RESOURCE_TYPE, true)
		}

	case common.GCP_BIGQUERYDATASET_RESOURCE_TYPE:

		if kmsKey, ok := entityJSONMap["kmsKey"].(string); ok && len(kmsKey) > 0 {
			kmsKey = strings.ToLower(kmsKey)
			kmsDocID := common.GenerateCombinedHashID(kmsKey, common.GCP_KMS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType, kmsKey, kmsDocID, common.GCP_KMS_RESOURCE_TYPE, true)
		}

	case common.GCP_VERTEXFEATUREVIEW_RESOURCE_TYPE:

		if bigQueryTableID, ok := entityJSONMap["bigQueryTableId"].(string); ok && len(bigQueryTableID) > 0 {

			bigQueryTableID = strings.ToLower(bigQueryTableID)

			bigQueryTableDocID := common.GenerateCombinedHashID(bigQueryTableID, common.GCP_BIGQUERYTABLE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

			assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				bigQueryTableID, bigQueryTableDocID, common.GCP_BIGQUERYTABLE_RESOURCE_TYPE, true)
		}

		if bigQueryDataSetID, ok := entityJSONMap["bigQueryDatasetId"].(string); ok && len(bigQueryDataSetID) > 0 {

			bigQueryDataSetID = strings.ToLower(bigQueryDataSetID)

			bigQueryDatasetDocID := common.GenerateCombinedHashID(bigQueryDataSetID, common.GCP_BIGQUERYDATASET_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

			assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				bigQueryDataSetID, bigQueryDatasetDocID, common.GCP_BIGQUERYDATASET_RESOURCE_TYPE, true)
		}

		if vertexFeatureStoreID, ok := entityJSONMap["featureStoreId"].(string); ok && len(vertexFeatureStoreID) > 0 {

			vertexFeatureStoreID = strings.ToLower(vertexFeatureStoreID)

			vertexFeatureStoreDocID := common.GenerateCombinedHashID(vertexFeatureStoreID, common.GCP_VERTEXFEATURESTORE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

			assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				vertexFeatureStoreID, vertexFeatureStoreDocID, common.GCP_VERTEXFEATURESTORE_RESOURCE_TYPE, true)
		}

	case common.GCP_VERTEXAIMODEL_RESOURCE_TYPE:

		if buckets, ok := entityJSONMap["bucketNames"].([]any); ok {
			for _, bucket := range buckets {
				if bucketID, ok := bucket.(string); ok && len(bucketID) > 0 {
					bucketID = strings.ToLower(bucketID)

					// Bucket can be from any account
					cloudStorage, err := common.GetCloudResourceDocumentForEntityIDAndType(bucketID, common.GCP_CLOUDSTORAGE_RESOURCE_TYPE, resourceContextDoc.TenantID, resourceContext.LastCollectedAt)
					if err != nil {
						break
					}

					if accountID, ok := cloudStorage["accountId"].(string); ok && len(accountID) > 0 {
						bucketDocID := common.GenerateCombinedHashID(bucketID, common.GCP_CLOUDSTORAGE_RESOURCE_TYPE, accountID, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
						assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
							bucketID, bucketDocID, common.GCP_CLOUDSTORAGE_RESOURCE_TYPE, true)
					}
				}
			}
		}

		if containerImages, ok := entityJSONMap["containerRegistryIds"].([]any); ok {
			for _, containerImage := range containerImages {
				if imageTag, ok := containerImage.(string); ok && len(imageTag) > 0 {
					imageTag = strings.ToLower(imageTag)
					// Image can be from any account
					repo := path.Dir(imageTag)
					tag := path.Base(imageTag)
					tagSplit := strings.Split(tag, ":")
					if len(tagSplit) > 1 {
						repo = repo + "/" + tagSplit[0]
						tag = strings.Join(tagSplit[1:], ":")

						imageDocs, err := common.GetCloudResourceDocumentFromEntityAndJSON(repo, common.GCP_DOCKERIMAGE_RESOURCE_TYPE, tag, resourceContextDoc.TenantID, resourceContext.LastCollectedAt)
						if err != nil {
							break
						}

						for _, imageDoc := range imageDocs {

							if imageEntityJSONString, ok := imageDoc["entityJson"].(string); ok && len(imageEntityJSONString) > 0 {

								imageEntityJSONMap := make(map[string]any)

								if err = json.Unmarshal([]byte(imageEntityJSONString), &imageEntityJSONMap); err != nil {
									continue
								}

								if tags, ok := imageEntityJSONMap["tags"].(string); ok && len(tags) > 0 {

									if strings.Contains(tags, tag) {
										if accountID, ok := imageDoc["accountId"].(string); ok && len(accountID) > 0 {
											if containerImageId, ok := imageDoc["entityId"].(string); ok {
												imageDocID := common.GenerateCombinedHashID(containerImageId, common.GCP_DOCKERIMAGE_RESOURCE_TYPE, accountID, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
												assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
													containerImageId, imageDocID, common.GCP_DOCKERIMAGE_RESOURCE_TYPE, true)
											}
										}

										break
									}
								}
							}
						}
					}
				}
			}
		}

	case common.GCP_VECTORSEARCHINDEXENDPOINT_RESOURCE_TYPE:

		if deployedIndexes, ok := entityJSONMap["deployedIndexes"].([]any); ok {
			for _, deployedIndex := range deployedIndexes {
				if deployedIndexMap, ok := deployedIndex.(map[string]any); ok {
					if searchIndex, ok := deployedIndexMap["index"].(string); ok && len(searchIndex) > 0 {
						searchIndex = strings.ToLower(searchIndex)
						searchIndexDocID := common.GenerateCombinedHashID(searchIndex, common.GCP_VECTORSEARCHINDEXES_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
						assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
							searchIndex, searchIndexDocID, common.GCP_VECTORSEARCHINDEXES_RESOURCE_TYPE, true)
					}
				}
			}
		}

	case common.GCP_NOTEBOOKRUNTIME_RESOURCE_TYPE:

		if notebookTemplateRef, ok := entityJSONMap["notebookRuntimeTemplateRef"].(map[string]any); ok {

			if notebookTemplate, ok := notebookTemplateRef["notebookRuntimeTemplate"].(string); ok {

				notebookTemplate = strings.ToLower(notebookTemplate)
				notebookTemplateDocID := common.GenerateCombinedHashID(notebookTemplate, common.GCP_NOTEBOOKRUNTIMETEMPLATE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

				assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
					notebookTemplate, notebookTemplateDocID, common.GCP_NOTEBOOKRUNTIMETEMPLATE_RESOURCE_TYPE, true)
			}
		}

	case common.GCP_VERTEXAINOTEBOOKINSTANCE_RESOURCE_TYPE:

		if serviceAccounts, ok := entityJSONMap["serviceAccounts"].([]any); ok {
			for _, sa := range serviceAccounts {
				if serviceAccountMap, ok := sa.(map[string]any); ok {
					if serviceAccount, ok := serviceAccountMap["email"].(string); ok {

						serviceAccount = strings.ToLower(serviceAccount)
						serviceAccountDocID := common.GenerateCombinedHashID(serviceAccount, common.GCP_SERVICEACCOUNT_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

						assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
							serviceAccount, serviceAccountDocID, common.GCP_SERVICEACCOUNT_RESOURCE_TYPE, true)
					}
				}
			}
		}

		if subNetworks, ok := entityJSONMap["subnetIds"].([]any); ok {
			for _, subNetwork := range subNetworks {
				if subNetworkID, ok := subNetwork.(string); ok && len(subNetworkID) > 0 {
					subNetworkID = strings.ToLower(subNetworkID)
					subNetworkDocID := common.GenerateCombinedHashID(subNetworkID, common.GCP_SUBNETWORK_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						subNetworkID, subNetworkDocID, common.GCP_SUBNETWORK_RESOURCE_TYPE, true)
				}
			}
		}

		if networks, ok := entityJSONMap["networkIds"].([]any); ok {
			for _, network := range networks {
				if networkID, ok := network.(string); ok && len(networkID) > 0 {
					networkID = strings.ToLower(networkID)
					networkDocID := common.GenerateCombinedHashID(networkID, common.GCP_NETWORK_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						networkID, networkDocID, common.GCP_NETWORK_RESOURCE_TYPE, true)
				}
			}
		}

	case common.GCP_VERTEXAITENSORBOARD_RESOURCE_TYPE:

		if bucketID, ok := entityJSONMap["blobStoragePath"].(string); ok && len(bucketID) > 0 {

			bucketID = strings.ToLower(bucketID)

			// Bucket can be from any account
			cloudStorage, err := common.GetCloudResourceDocumentForEntityIDAndType(bucketID, common.GCP_CLOUDSTORAGE_RESOURCE_TYPE, resourceContextDoc.TenantID, resourceContext.LastCollectedAt)
			if err != nil {
				break
			}

			if accountID, ok := cloudStorage["accountId"].(string); ok && len(accountID) > 0 {
				bucketDocID := common.GenerateCombinedHashID(bucketID, common.GCP_CLOUDSTORAGE_RESOURCE_TYPE, accountID, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
				assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
					bucketID, bucketDocID, common.GCP_CLOUDSTORAGE_RESOURCE_TYPE, true)
			}
		}

	case common.GCP_DOCUMENTAIPROCESSOR_RESOURCE_TYPE:

		if processorVersions, ok := entityJSONMap["processorVersions"].([]any); ok {
			for _, processorVersion := range processorVersions {
				if processorVersionString, ok := processorVersion.(string); ok {

					processorVersionString = strings.ToLower(processorVersionString)
					processorVersionDocID := common.GenerateCombinedHashID(processorVersionString, common.GCP_DOCUMENTAIPROCESSORVERSION_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						processorVersionString, processorVersionDocID, common.GCP_DOCUMENTAIPROCESSORVERSION_RESOURCE_TYPE, true)
				}
			}
		}

	case common.GCP_GKECLUSTER_RESOURCE_TYPE:
		if instanceGroupManagers, ok := entityJSONMap["instanceGroupManagers"].([]any); ok {
			for _, instanceGroupManager := range instanceGroupManagers {
				if instanceGroupManager, ok := instanceGroupManager.(string); ok {

					instanceGroupManagerDocID := common.GenerateCombinedHashID(instanceGroupManager, common.GCP_INSTANCEGROUPMANAGER_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						instanceGroupManager, instanceGroupManagerDocID, common.GCP_INSTANCEGROUPMANAGER_RESOURCE_TYPE, true)

				}
			}
		}

	case common.GCP_GKENAMESPACE_RESOURCE_TYPE, common.GCP_GKECLUSTERROLE_RESOURCE_TYPE:
		if clusterId, ok := entityJSONMap["clusterEntityId"].(string); ok {
			clusterDocID := common.GenerateCombinedHashID(clusterId, common.GCP_GKECLUSTER_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			assignRelatedParentResourceOneWay(resourceContext, resourceDocID, clusterId, clusterDocID, common.GCP_GKECLUSTER_RESOURCE_TYPE, true)
		}

		assignRelatedResourcesFromSingleResourceCollector(resourceContext, resourceContextDoc, resourceDocID, entityJSONMap)

	case common.GCP_GKENAMESPACEROLE_RESOURCE_TYPE, common.GCP_GKESERVICEACCOUNT_RESOURCE_TYPE, common.GCP_GKEREPLICASET_RESOURCE_TYPE, common.GCP_GKEDAEMONSET_RESOURCE_TYPE:
		if clusterId, ok := entityJSONMap["clusterEntityId"].(string); ok {
			clusterDocID := common.GenerateCombinedHashID(clusterId, common.GCP_GKECLUSTER_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			assignRelatedParentResourceOneWay(resourceContext, resourceDocID, clusterId, clusterDocID, common.GCP_GKECLUSTER_RESOURCE_TYPE, true)
		}

		if namespaceId, ok := entityJSONMap["namespace"].(string); ok {
			clusterDocID := common.GenerateCombinedHashID(namespaceId, common.GCP_GKECLUSTER_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			assignRelatedParentResourceOneWay(resourceContext, resourceDocID, namespaceId, clusterDocID, common.GCP_GKENAMESPACE_RESOURCE_TYPE, true)
		}

		assignRelatedResourcesFromSingleResourceCollector(resourceContext, resourceContextDoc, resourceDocID, entityJSONMap)

	case common.GCP_GKESERVICE_RESOURCE_TYPE:
		if namespaceId, ok := entityJSONMap["namespace"].(string); ok {
			clusterDocID := common.GenerateCombinedHashID(namespaceId, common.GCP_GKECLUSTER_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			assignRelatedParentResourceOneWay(resourceContext, resourceDocID, namespaceId, clusterDocID, common.GCP_GKENAMESPACE_RESOURCE_TYPE, true)
		}

		assignRelatedResourcesFromSingleResourceCollector(resourceContext, resourceContextDoc, resourceDocID, entityJSONMap)

	case common.GCP_APPENGINESERVICE_RESOURCE_TYPE:
		if appEngineApplication, ok := entityJSONMap["appName"].(string); ok {

			appEngineApplicationDocID := common.GenerateCombinedHashID(appEngineApplication, common.GCP_APPENGINEAPPLICATION_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				appEngineApplication, appEngineApplicationDocID, common.GCP_APPENGINEAPPLICATION_RESOURCE_TYPE, true)
		}

	case common.GCP_APPENGINESERVICEVERSION_RESOURCE_TYPE:
		if appEngineApplication, ok := entityJSONMap["serviceName"].(string); ok {

			appEngineApplicationDocID := common.GenerateCombinedHashID(appEngineApplication, common.GCP_APPENGINEAPPLICATION_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				appEngineApplication, appEngineApplicationDocID, common.GCP_APPENGINEAPPLICATION_RESOURCE_TYPE, true)
		}

	case common.GCP_CONTAINERINSTANCETEMPLATE_RESOURCE_TYPE:
		if networks, ok := entityJSONMap["networks"].([]any); ok {

			for _, network := range networks {

				if network, ok := network.(string); ok && len(network) > 0 {

					networkDocID := common.GenerateCombinedHashID(network, common.AZURE_ACTIONGROUP_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						network, networkDocID, common.GCP_NETWORK_RESOURCE_TYPE, true)
				}
			}
		}

		if subNetworks, ok := entityJSONMap["subNetworks"].([]any); ok {

			for _, subNetwork := range subNetworks {

				if subNetwork, ok := subNetwork.(string); ok && len(subNetwork) > 0 {

					subNetworkDocID := common.GenerateCombinedHashID(subNetwork, common.AZURE_ACTIONGROUP_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						subNetwork, subNetworkDocID, common.GCP_SUBNETWORK_RESOURCE_TYPE, true)
				}
			}
		}

		var clusterLocation, clusterName string

		if tags, ok := entityJSONMap["tags"].([]any); ok {
			for _, tag := range tags {
				if tagMap, ok := tag.(map[string]any); ok {
					if tagKey, ok := tagMap["key"].(string); ok && len(tagKey) > 0 {
						if tagValue, ok := tagMap["value"].(string); ok && len(tagValue) > 0 {

							if tagKey == "goog-k8s-cluster-location" {
								clusterLocation = tagValue
							}

							if tagKey == "goog-k8s-cluster-name" {
								clusterName = tagValue
							}
						}
					}
				}
			}
		}

		if len(clusterName) > 0 && len(clusterLocation) > 0 {

			parts := strings.SplitN(resourceContextDoc.ResourceID, "/", 3) // only need first 2 or 3 segments
			if len(parts) >= 2 && parts[0] == "projects" {

				clusterID := strings.ToLower("projects/" + parts[1] + "/locations/" + clusterLocation + "/clusters/" + clusterName)

				clusterDocID := common.GenerateCombinedHashID(clusterID, common.GCP_GKECLUSTER_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

				assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
					clusterID, clusterDocID, common.GCP_GKECLUSTER_RESOURCE_TYPE, true)
			}
		}

	case common.AZURE_VM_RESOURCE_TYPE:

		if nwInterfaces, ok := entityJSONMap["azureNetworkInterfaces"].([]any); ok {

			for _, nwInterface := range nwInterfaces {

				if nwInterfaceMap, ok := nwInterface.(map[string]any); ok {

					if nwInterfaceID, ok := nwInterfaceMap["id"].(string); ok && len(nwInterfaceID) > 0 {

						nwInterfaceID = strings.ToLower(nwInterfaceID)

						nwInterfaceDocID := common.GenerateCombinedHashID(nwInterfaceID, common.NETWORKINTERFACE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

						assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
							nwInterfaceID, nwInterfaceDocID, common.NETWORKINTERFACE_RESOURCE_TYPE, true)
					}

					if nwProperties, ok := nwInterfaceMap["properties"].(map[string]any); ok {

						if nwSecurityGroup, ok := nwProperties["networkSecurityGroup"].(map[string]any); ok {

							if nsgID, ok := nwSecurityGroup["id"].(string); ok && len(nsgID) > 0 {

								nsgID = strings.ToLower(nsgID)

								nsgDocID := common.GenerateCombinedHashID(nsgID, common.AZURE_NSG_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

								assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
									nsgID, nsgDocID, common.AZURE_NSG_RESOURCE_TYPE, true)
							}
						}

						if ipConfigs, ok := nwProperties["ipConfigurations"].([]any); ok {

							for _, ipConfig := range ipConfigs {

								if ipConfigMap, ok := ipConfig.(map[string]any); ok {

									if ipConfigProperties, ok := ipConfigMap["properties"].(map[string]any); ok {

										if subnet, ok := ipConfigProperties["subnet"].(map[string]any); ok {

											if subnetID, ok := subnet["id"].(string); ok && len(subnetID) > 0 {

												subnetID = strings.ToLower(subnetID)

												subnetDocID := common.GenerateCombinedHashID(subnetID, common.AZURE_SUBNET_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

												assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
													subnetID, subnetDocID, common.AZURE_SUBNET_RESOURCE_TYPE, true)
											}
										}

										if publicIpAddress, ok := ipConfigProperties["publicIPAddress"].(map[string]any); ok {

											if publicIpAddressID, ok := publicIpAddress["id"].(string); ok && len(publicIpAddressID) > 0 {

												publicIpAddressID = strings.ToLower(publicIpAddressID)

												publicIPAddressDocID := common.GenerateCombinedHashID(publicIpAddressID, common.AZURE_PUBLICIP_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

												assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
													publicIpAddressID, publicIPAddressDocID, common.AZURE_PUBLICIP_RESOURCE_TYPE, true)
											}
										}
									}
								}
							}
						}
					}
				}
			}
		}

		if publicIP, ok := entityJSONMap["publicIp"].(string); ok {

			publicIPAddressDocID := common.GenerateCombinedHashID(publicIP, common.AZURE_IPDETAILS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

			assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				publicIP, publicIPAddressDocID, common.AZURE_IPDETAILS_RESOURCE_TYPE, true)
		}

		if scaleSetID, ok := entityJSONMap["scaleSetId"].(string); ok {

			scaleSetID = strings.ToLower(scaleSetID)

			scaleSetDocID := common.GenerateCombinedHashID(scaleSetID, common.AZURE_VMSCALESET_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

			assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				scaleSetID, scaleSetDocID, common.AZURE_VMSCALESET_RESOURCE_TYPE, true)
		}

	case common.AZURE_SUBNET_RESOURCE_TYPE:

		if virtualNetworkID, ok := entityJSONMap["virtualNetworkId"].(string); ok && len(virtualNetworkID) > 0 {

			virtualNetworkID = strings.ToLower(virtualNetworkID)
			virtualNetworkDocID := common.GenerateCombinedHashID(virtualNetworkID, common.AZURE_VIRTUALNETWORK_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

			assignRelatedParentResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				virtualNetworkID, virtualNetworkDocID, common.AZURE_VIRTUALNETWORK_RESOURCE_TYPE, true)
		}

	case common.AZURE_VMDISK_RESOURCE_TYPE:

		if vmID, ok := entityJSONMap["managedBy"].(string); ok && len(vmID) > 0 {

			vmID = strings.ToLower(vmID)

			vmDocID := common.GenerateCombinedHashID(vmID, common.AZURE_VM_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

			assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				vmID, vmDocID, common.AZURE_VM_RESOURCE_TYPE, true)
		}

	case common.AZURE_SNAPSHOT_RESOURCE_TYPE:

		if snapShotProperties, ok := entityJSONMap["properties"].(map[string]any); ok {

			if snapShotCreationData, ok := snapShotProperties["creationData"].(map[string]any); ok {

				if vmDiskID, ok := snapShotCreationData["sourceResourceId"].(string); ok && len(vmDiskID) > 0 {

					vmDiskID = strings.ToLower(vmDiskID)

					vmDiskDocID := common.GenerateCombinedHashID(vmDiskID, common.AZURE_VMDISK_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

					assignRelatedParentResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						vmDiskID, vmDiskDocID, common.AZURE_VMDISK_RESOURCE_TYPE, true)
				}
			}
		}

	case common.AZURE_NATGATEWAY_RESOURCE_TYPE:

		if natProperties, ok := entityJSONMap["properties"].(map[string]any); ok {

			if subnets, ok := natProperties["subnets"].([]any); ok {

				for _, subnet := range subnets {

					if subnetMap, ok := subnet.(map[string]any); ok {

						if subnetID, ok := subnetMap["id"].(string); ok && len(subnetID) > 0 {

							subnetID = strings.ToLower(subnetID)

							subnetDocID := common.GenerateCombinedHashID(subnetID, common.AZURE_SUBNET_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

							assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
								subnetID, subnetDocID, common.AZURE_SUBNET_RESOURCE_TYPE, true)
						}
					}
				}
			}

			if publicIpAddresses, ok := natProperties["publicIpAddresses"].([]any); ok {

				for _, publicIpAddress := range publicIpAddresses {

					if publicIpAddressMap, ok := publicIpAddress.(map[string]any); ok {

						if publicIpAddressID, ok := publicIpAddressMap["id"].(string); ok && len(publicIpAddressID) > 0 {

							publicIpAddressID = strings.ToLower(publicIpAddressID)

							publicIPAddressDocID := common.GenerateCombinedHashID(publicIpAddressID, common.AZURE_PUBLICIP_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

							assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
								publicIpAddressID, publicIPAddressDocID, common.AZURE_PUBLICIP_RESOURCE_TYPE, true)
						}
					}
				}
			}
		}

	case common.AZURE_SQLDB_RESOURCE_TYPE:

		if sqlServerName, ok := entityJSONMap["sqlServerName"].(string); ok && len(sqlServerName) > 0 {

			var sqlServerPrefix string

			splitName := strings.Split(resourceContextDoc.ResourceID, "/")
			for i, v := range splitName {
				if v == "servers" {
					sqlServerPrefix = strings.Join(splitName[:i+1], "/")
					break
				}
			}

			if len(sqlServerPrefix) > 0 {

				sqlServerID := sqlServerPrefix + "/" + strings.ToLower(sqlServerName)

				sqlServerDocID := common.GenerateCombinedHashID(sqlServerID, common.AZURE_SQLSERVER_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

				assignRelatedParentResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
					sqlServerID, sqlServerDocID, common.AZURE_SQLSERVER_RESOURCE_TYPE, true)
			}
		}

	case common.AZURE_ARCDATASQLDB_RESOURCE_TYPE:

		if arcDataInstanceName, ok := entityJSONMap["instance"].(string); ok && len(arcDataInstanceName) > 0 {

			var arcDataInstancePrefix string

			splitName := strings.Split(resourceContextDoc.ResourceID, "/")
			for i, v := range splitName {
				if v == "sqlserverinstances" {
					arcDataInstancePrefix = strings.Join(splitName[:i+1], "/")
					break
				}
			}

			if len(arcDataInstancePrefix) > 0 {

				arcDataInstanceId := arcDataInstancePrefix + "/" + strings.ToLower(arcDataInstanceName)

				arcDataInstanceDocID := common.GenerateCombinedHashID(arcDataInstanceId, common.AZURE_ARCDATASQLINSTANCE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

				assignRelatedParentResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
					arcDataInstanceId, arcDataInstanceDocID, common.AZURE_ARCDATASQLINSTANCE_RESOURCE_TYPE, true)
			}
		}

	case common.AZURE_MLWORKSPACE_RESOURCE_TYPE:

		if properties, ok := entityJSONMap["properties"].(map[string]any); ok {

			if storageAccountID, ok := properties["storageAccount"].(string); ok && len(storageAccountID) > 0 {

				storageAccountID = strings.ToLower(storageAccountID)
				storageAccountDocID := common.GenerateCombinedHashID(storageAccountID, common.AZURE_STORAGEACCOUNT_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

				assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
					storageAccountID, storageAccountDocID, common.AZURE_STORAGEACCOUNT_RESOURCE_TYPE, true)
			}

			if keyVaultID, ok := properties["keyVault"].(string); ok && len(keyVaultID) > 0 {

				keyVaultID = strings.ToLower(keyVaultID)
				keyVaultDocID := common.GenerateCombinedHashID(keyVaultID, common.AZURE_KEYVAULT_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

				assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
					keyVaultID, keyVaultDocID, common.AZURE_KEYVAULT_RESOURCE_TYPE, true)
			}

			if containerRegistryID, ok := properties["containerRegistry"].(string); ok && len(containerRegistryID) > 0 {

				containerRegistryID = strings.ToLower(containerRegistryID)
				containerRegistryDocID := common.GenerateCombinedHashID(containerRegistryID, common.AZURE_CONTAINERREGISTRY_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

				assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
					containerRegistryID, containerRegistryDocID, common.AZURE_CONTAINERREGISTRY_RESOURCE_TYPE, true)
			}
		}

	case common.AZURE_MLWORKSPACEDATASTORE_RESOURCE_TYPE, common.AZURE_MLWORKSPACEMODELS_RESOURCE_TYPE,
		common.AZURE_MLWORKSPACEJOBS_RESOURCE_TYPE, common.AZURE_MLWORKSPACECOMPUTEMACHINE_RESOURCE_TYPE,
		common.AZURE_MLWORKSPACEDEPLOYMENTS_RESOURCE_TYPE, common.AZURE_MLWORKSPACEENDPOINTS_RESOURCE_TYPE:

		if mlWorkspaceName, ok := entityJSONMap["workspaceName"].(string); ok && len(mlWorkspaceName) > 0 {

			var mlWorkspacePrefix string

			splitName := strings.Split(resourceContextDoc.ResourceID, "/")
			for i, v := range splitName {
				if v == "workspaces" {
					mlWorkspacePrefix = strings.Join(splitName[:i+1], "/")
					break
				}
			}

			if len(mlWorkspacePrefix) > 0 {

				mlWorkspaceID := mlWorkspacePrefix + "/" + strings.ToLower(mlWorkspaceName)

				mlWorkspaceDocID := common.GenerateCombinedHashID(mlWorkspaceID, common.AZURE_MLWORKSPACE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

				assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
					mlWorkspaceID, mlWorkspaceDocID, common.AZURE_MLWORKSPACE_RESOURCE_TYPE, true)
			}
		}

		if resourceContextDoc.ResourceType == common.AZURE_MLWORKSPACEDEPLOYMENTS_RESOURCE_TYPE ||
			resourceContextDoc.ResourceType == common.AZURE_MLWORKSPACEJOBS_RESOURCE_TYPE {

			if mlWorkspaceCompute, ok := entityJSONMap["computeId"].(string); ok && len(mlWorkspaceCompute) > 0 {

				mlWorkspaceComputeID := strings.ToLower(mlWorkspaceCompute)

				mlWorkspaceComputeDocID := common.GenerateCombinedHashID(mlWorkspaceComputeID, common.AZURE_MLWORKSPACECOMPUTEMACHINE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

				assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
					mlWorkspaceComputeID, mlWorkspaceComputeDocID, common.AZURE_MLWORKSPACECOMPUTEMACHINE_RESOURCE_TYPE, true)

			}
		}

		if resourceContextDoc.ResourceType == common.AZURE_MLWORKSPACEDEPLOYMENTS_RESOURCE_TYPE {

			if mlWorkspaceModel, ok := entityJSONMap["model"].(string); ok && len(mlWorkspaceModel) > 0 {

				var mlWorkspaceModelPrefix string

				splitName := strings.Split(resourceContextDoc.ResourceID, "/")
				for i, v := range splitName {
					if v == "workspaces" {
						if len(splitName) >= i+2 {
							mlWorkspaceModelPrefix = strings.Join(splitName[:i+2], "/")
							break
						}
					}
				}

				if len(mlWorkspaceModelPrefix) > 0 {

					mlWorkspaceModelID := mlWorkspaceModelPrefix + "/models/" + strings.ToLower(mlWorkspaceModel)

					mlWorkspaceModelDocID := common.GenerateCombinedHashID(mlWorkspaceModelID, common.AZURE_MLWORKSPACEMODELS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						mlWorkspaceModelID, mlWorkspaceModelDocID, common.AZURE_MLWORKSPACEMODELS_RESOURCE_TYPE, true)

				}
			}

		} else if resourceContextDoc.ResourceType == common.AZURE_MLWORKSPACEENDPOINTS_RESOURCE_TYPE {

			if deployments, ok := entityJSONMap["deployments"].([]any); ok {

				for _, deployment := range deployments {

					if deploymentString, ok := deployment.(string); ok && len(deploymentString) > 0 {
						deploymentID := resourceContextDoc.ResourceID + "/deployments/" + strings.ToLower(deploymentString)

						deploymentDocID := common.GenerateCombinedHashID(deploymentID, common.AZURE_MLWORKSPACEDEPLOYMENTS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

						assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
							deploymentID, deploymentDocID, common.AZURE_MLWORKSPACEDEPLOYMENTS_RESOURCE_TYPE, true)
					}
				}
			}

			if models, ok := entityJSONMap["models"].([]any); ok {

				var mlWorkspaceModelPrefix string

				splitName := strings.Split(resourceContextDoc.ResourceID, "/")
				for i, v := range splitName {
					if v == "workspaces" {
						if len(splitName) >= i+2 {
							mlWorkspaceModelPrefix = strings.Join(splitName[:i+2], "/")
							break
						}
					}
				}

				if len(mlWorkspaceModelPrefix) > 0 {

					for _, model := range models {

						if modelString, ok := model.(string); ok && len(modelString) > 0 {

							modelID := mlWorkspaceModelPrefix + "/models/" + strings.ToLower(modelString)

							modelDocID := common.GenerateCombinedHashID(modelID, common.AZURE_MLWORKSPACEMODELS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

							assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
								modelID, modelDocID, common.AZURE_MLWORKSPACEMODELS_RESOURCE_TYPE, true)
						}
					}
				}
			}
		} else if resourceContextDoc.ResourceType == common.AZURE_MLWORKSPACEMODELS_RESOURCE_TYPE {

			var mlWorkspace string

			splitName := strings.Split(resourceContextDoc.ResourceID, "/")
			for i, v := range splitName {
				if v == "models" {
					mlWorkspace = strings.Join(splitName[:i], "/")
					break
				}
			}

			if len(mlWorkspace) > 0 {

				if mlExperiment, ok := entityJSONMap["experimentId"].(string); ok && len(mlExperiment) > 0 {

					mlExperimentID := mlWorkspace + "/experiments/" + strings.ToLower(mlExperiment)

					mlExperimentDocID := common.GenerateCombinedHashID(mlExperimentID, common.AZURE_MLWORKSPACEEXPERIMENTS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						mlExperimentID, mlExperimentDocID, common.AZURE_MLWORKSPACEEXPERIMENTS_RESOURCE_TYPE, true)
				}

				if mlJob, ok := entityJSONMap["runId"].(string); ok && len(mlJob) > 0 {

					mlJobID := mlWorkspace + "/jobs/" + strings.ToLower(mlJob)

					mlJobDocID := common.GenerateCombinedHashID(mlJobID, common.AZURE_MLWORKSPACEJOBS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						mlJobID, mlJobDocID, common.AZURE_MLWORKSPACEJOBS_RESOURCE_TYPE, true)
				}

				if mlDataStore, ok := entityJSONMap["dataStore"].(string); ok && len(mlDataStore) > 0 {

					mlDataStoreID := mlWorkspace + "/datastores/" + strings.ToLower(mlDataStore)

					mlDataStoreDocID := common.GenerateCombinedHashID(mlDataStoreID, common.AZURE_MLWORKSPACEDATASTORE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						mlDataStoreID, mlDataStoreDocID, common.AZURE_MLWORKSPACEDATASTORE_RESOURCE_TYPE, true)
				}
			}
		} else if resourceContextDoc.ResourceType == common.AZURE_MLWORKSPACEJOBS_RESOURCE_TYPE {

			var mlWorkspace string

			splitName := strings.Split(resourceContextDoc.ResourceID, "/")
			for i, v := range splitName {
				if v == "jobs" {
					mlWorkspace = strings.Join(splitName[:i], "/")
					break
				}
			}

			if len(mlWorkspace) > 0 {

				if mlExperiment, ok := entityJSONMap["experimentId"].(string); ok && len(mlExperiment) > 0 {

					mlExperimentID := mlWorkspace + "/experiments/" + strings.ToLower(mlExperiment)

					mlExperimentDocID := common.GenerateCombinedHashID(mlExperimentID, common.AZURE_MLWORKSPACEEXPERIMENTS_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						mlExperimentID, mlExperimentDocID, common.AZURE_MLWORKSPACEEXPERIMENTS_RESOURCE_TYPE, true)
				}

				if mlDataAsset, ok := entityJSONMap["dataAsset"].(string); ok && len(mlDataAsset) > 0 {

					mlDataAssetID := mlWorkspace + "/data/" + strings.ToLower(mlDataAsset)

					mlDataAssetDocID := common.GenerateCombinedHashID(mlDataAssetID, common.AZURE_MLWORKSPACEDATAASSET_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						mlDataAssetID, mlDataAssetDocID, common.AZURE_MLWORKSPACEDATAASSET_RESOURCE_TYPE, true)
				}

				if mlDatastoreID, ok := entityJSONMap["blobStoreId"].(string); ok && len(mlDatastoreID) > 0 {
					mlDatastoreID := strings.ToLower(mlDatastoreID)

					mlDataStoreDocID := common.GenerateCombinedHashID(mlDatastoreID, common.AZURE_MLWORKSPACEDATASTORE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						mlDatastoreID, mlDataStoreDocID, common.AZURE_MLWORKSPACEDATASTORE_RESOURCE_TYPE, true)
				}
			}
		}

	case common.AZURE_MLHUBCONNECTION_RESOURCE_TYPE:

		if properties, ok := entityJSONMap["properties"].(map[string]any); ok {

			if mlWorkspaceName, ok := properties["workspace"].(string); ok && len(mlWorkspaceName) > 0 {

				var mlWorkspacePrefix string

				splitName := strings.Split(resourceContextDoc.ResourceID, "/")
				for i, v := range splitName {
					if v == "workspaces" {
						mlWorkspacePrefix = strings.Join(splitName[:i+1], "/")
						break
					}
				}

				if len(mlWorkspacePrefix) > 0 {

					mlWorkspaceID := mlWorkspacePrefix + "/" + strings.ToLower(mlWorkspaceName)

					mlWorkspaceDocID := common.GenerateCombinedHashID(mlWorkspaceID, common.AZURE_MLWORKSPACE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						mlWorkspaceID, mlWorkspaceDocID, common.AZURE_MLWORKSPACE_RESOURCE_TYPE, true)
				}
			}
		}

	case common.AZURE_OPENAIMODEL_RESOURCE_TYPE, common.AZURE_OPENAIDEPLOYMENT_RESOURCE_TYPE,
		common.AZURE_OPENAICONTENTFILTER_RESOURCE_TYPE, common.AZURE_OPENAICONTENTFILTERBL_RESOURCE_TYPE:

		if openAIAccountName, ok := entityJSONMap["accountName"].(string); ok && len(openAIAccountName) > 0 {

			var openAIAccountNamePrefix string

			splitName := strings.Split(resourceContextDoc.ResourceID, "/")
			for i, v := range splitName {
				if v == "accounts" {
					openAIAccountNamePrefix = strings.Join(splitName[:i+1], "/")
					break
				}
			}

			if len(openAIAccountNamePrefix) > 0 {

				openAIAccountID := openAIAccountNamePrefix + "/" + strings.ToLower(openAIAccountName)

				openAIAccountDocID := common.GenerateCombinedHashID(openAIAccountID, common.AZURE_OPENAI_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

				assignRelatedParentResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
					openAIAccountID, openAIAccountDocID, common.AZURE_OPENAI_RESOURCE_TYPE, true)
			}
		}

		if resourceContextDoc.ResourceType == common.AZURE_OPENAIDEPLOYMENT_RESOURCE_TYPE {

			if openAIModelID, ok := entityJSONMap["modelId"].(string); ok && len(openAIModelID) > 0 {

				openAIModelID = strings.ToLower(openAIModelID)
				openAIModelDocID := common.GenerateCombinedHashID(openAIModelID, common.AZURE_OPENAIMODEL_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

				assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
					openAIModelID, openAIModelDocID, common.AZURE_OPENAIMODEL_RESOURCE_TYPE, true)
			}

			if contentFilterID, ok := entityJSONMap["cfId"].(string); ok && len(contentFilterID) > 0 {

				contentFilterID = strings.ToLower(contentFilterID)
				contentFilterDocID := common.GenerateCombinedHashID(contentFilterID, common.AZURE_OPENAICONTENTFILTER_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

				assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
					contentFilterID, contentFilterDocID, common.AZURE_OPENAICONTENTFILTER_RESOURCE_TYPE, true)
			}
		}

	case common.AZURE_METRICALERT_RESOURCE_TYPE:
		if actionGroupIds, ok := entityJSONMap["actionGroupIds"].([]any); ok {

			for _, actionGroupId := range actionGroupIds {

				if actionGroupId, ok := actionGroupId.(string); ok && len(actionGroupId) > 0 {

					actionGroupIdDocID := common.GenerateCombinedHashID(actionGroupId, common.AZURE_ACTIONGROUP_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						actionGroupId, actionGroupIdDocID, common.AZURE_ACTIONGROUP_RESOURCE_TYPE, true)
				}
			}
		}

	case common.AZURE_SCHEDULEDQUERYRULES_RESOURCE_TYPE:
		if actionGroupIds, ok := entityJSONMap["actionGroupIds"].([]any); ok {

			for _, actionGroupId := range actionGroupIds {

				if actionGroupId, ok := actionGroupId.(string); ok && len(actionGroupId) > 0 {

					actionGroupIdDocID := common.GenerateCombinedHashID(actionGroupId, common.AZURE_ACTIONGROUP_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						actionGroupId, actionGroupIdDocID, common.AZURE_ACTIONGROUP_RESOURCE_TYPE, true)
				}
			}
		}

	case common.AZURE_AKSCLUSTER_RESOURCE_TYPE:

		if identityProfile, ok := entityJSONMap["identityProfile"].(map[string]any); ok {
			if kubeletIdentity, ok := identityProfile["kubeletidentity"].(map[string]any); ok {
				if userAssignedIdentityID, ok := kubeletIdentity["resourceId"].(string); ok && len(userAssignedIdentityID) > 0 {

					userAssignedIdentityID = strings.ToLower(userAssignedIdentityID)
					userAssignedIdentityIDDocID := common.GenerateCombinedHashID(userAssignedIdentityID, common.AZURE_USERASSIGNEDIDENTITY_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						userAssignedIdentityID, userAssignedIdentityIDDocID, common.AZURE_USERASSIGNEDIDENTITY_RESOURCE_TYPE, true)
				}
			}
		}

		if networkProfile, ok := entityJSONMap["networkProfile"].(map[string]any); ok {
			if loadBalancerProfile, ok := networkProfile["loadBalancerProfile"].(map[string]any); ok {
				if outboundIPs, ok := loadBalancerProfile["effectiveOutboundIPs"].([]any); ok {

					for _, outboundIP := range outboundIPs {

						if outboundIPMap, ok := outboundIP.(map[string]any); ok {

							if publicIpAddressID, ok := outboundIPMap["id"].(string); ok && len(publicIpAddressID) > 0 {

								publicIpAddressID = strings.ToLower(publicIpAddressID)

								publicIPAddressDocID := common.GenerateCombinedHashID(publicIpAddressID, common.AZURE_PUBLICIP_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

								assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
									publicIpAddressID, publicIPAddressDocID, common.AZURE_PUBLICIP_RESOURCE_TYPE, true)
							}
						}
					}
				}
			}
		}

	case common.AZURE_K8NODE_RESOURCE_TYPE:

		if clusterName, ok := entityJSONMap["clusterName"].(string); ok && len(clusterName) >= 0 {

			var clusterPrefix string

			splitName := strings.Split(resourceContextDoc.ResourceID, "/")
			for i, v := range splitName {
				if v == "managedclusters" {
					clusterPrefix = strings.Join(splitName[:i], "/")
					break
				}
			}

			if len(clusterPrefix) > 0 {

				clusterID := clusterPrefix + "/providers/microsoft.containerservice/managedclusters/" + strings.ToLower(clusterName)

				clusterDocID := common.GenerateCombinedHashID(clusterID, common.AZURE_AKSCLUSTER_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

				assignRelatedParentResourceOneWay(resourceContext, resourceDocID, clusterID, clusterDocID,
					common.AZURE_AKSCLUSTER_RESOURCE_TYPE, true)
			}
		}

		if k8Spec, ok := entityJSONMap["spec"].(map[string]any); ok {
			if vmID, ok := k8Spec["providerID"].(string); ok && len(vmID) > 0 {
				vmID = strings.TrimPrefix(strings.ToLower(vmID), "azure://")

				vmDocID := common.GenerateCombinedHashID(vmID, common.AZURE_VM_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

				assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
					vmID, vmDocID, common.AZURE_VM_RESOURCE_TYPE, true)
			}
		}

		if status, ok := entityJSONMap["status"].(map[string]any); ok {
			if volumesInUse, ok := status["volumesInUse"].([]any); ok {

				for _, volumeInUse := range volumesInUse {

					if volumeInUseString, ok := volumeInUse.(string); ok && len(volumeInUseString) > 0 {

						var vmDiskID string

						volumeSplit := strings.Split(volumeInUseString, "^")
						if len(volumeSplit) > 0 {
							vmDiskID = strings.ToLower(volumeSplit[1])
						}

						vmDiskDocID := common.GenerateCombinedHashID(vmDiskID, common.AZURE_VMDISK_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

						assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
							vmDiskID, vmDiskDocID, common.AZURE_VMDISK_RESOURCE_TYPE, true)
					}
				}
			}
		}

	case common.AZURE_COMPUTEIMAGE_RESOURCE_TYPE:

		if properties, ok := entityJSONMap["properties"].(map[string]any); ok {

			if sourceVirtualMachine, ok := properties["sourceVirtualMachine"].(map[string]any); ok {

				if vmID, ok := sourceVirtualMachine["id"].(string); ok && len(vmID) > 0 {
					vmID = strings.ToLower(vmID)

					vmDocID := common.GenerateCombinedHashID(vmID, common.AZURE_VM_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						vmID, vmDocID, common.AZURE_VM_RESOURCE_TYPE, true)
				}
			}

			if storageProfile, ok := properties["storageProfile"].(map[string]any); ok {
				if osDisk, ok := storageProfile["osDisk"].(map[string]any); ok {
					if managedDisk, ok := osDisk["managedDisk"].(map[string]any); ok {
						if vmDiskID, ok := managedDisk["id"].(string); ok && len(vmDiskID) > 0 {

							vmDiskID = strings.ToLower(vmDiskID)

							vmDiskDocID := common.GenerateCombinedHashID(vmDiskID, common.AZURE_VMDISK_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

							assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
								vmDiskID, vmDiskDocID, common.AZURE_VMDISK_RESOURCE_TYPE, true)
						}
					}
				}
			}
		}

	case common.AZURE_GRAPHAPP_RESOURCE_TYPE:

		if alternativeNames, ok := entityJSONMap["alternativeNames"].([]any); ok {

			for _, alternativeName := range alternativeNames {

				if alternativeNameString, ok := alternativeName.(string); ok && len(alternativeNameString) > 0 {

					if strings.Contains(alternativeNameString, "isExplicit") || strings.Contains(alternativeNameString, "appRoleAssignmentRequired") {
						continue
					}

					userIdentityID := strings.ToLower(alternativeNameString)

					userIdentityDocID := common.GenerateCombinedHashID(userIdentityID, common.AZURE_USERASSIGNEDIDENTITY_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						userIdentityID, userIdentityDocID, common.AZURE_USERASSIGNEDIDENTITY_RESOURCE_TYPE, true)
				}
			}
		}

	case common.OPENAI_VECTORSTOREFILE_RESOURCE_TYPE:

		if vectorStoreID, ok := entityJSONMap["vector_store_id"].(string); ok && len(vectorStoreID) > 0 {

			vectorStoreID = strings.ToLower(vectorStoreID)

			vectorStoreDocID := common.GenerateCombinedHashID(vectorStoreID, common.OPENAI_VECTORSTORE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContextDoc.TenantID)

			assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				vectorStoreID, vectorStoreDocID, common.OPENAI_VECTORSTORE_RESOURCE_TYPE, true)
		}

	case common.OPENAI_FINETUNEDMODEL_RESOURCE_TYPE:

		if openAITrainingFile, ok := entityJSONMap["training_file"].(string); ok && len(openAITrainingFile) > 0 {

			openAITrainingFileDocID := common.GenerateCombinedHashID(openAITrainingFile, common.OPENAI_FILE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

			assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				openAITrainingFile, openAITrainingFileDocID, common.OPENAI_FILE_RESOURCE_TYPE, true)
		}

		if openAIValidationFile, ok := entityJSONMap["validation_file"].(string); ok && len(openAIValidationFile) > 0 {

			openAIValidationFileDocID := common.GenerateCombinedHashID(openAIValidationFile, common.OPENAI_FILE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

			assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				openAIValidationFile, openAIValidationFileDocID, common.OPENAI_FILE_RESOURCE_TYPE, true)
		}

		if resultFiles, ok := entityJSONMap["result_files"].([]any); ok {

			for _, resultFile := range resultFiles {

				if openAIResultFile, ok := resultFile.(string); ok && len(openAIResultFile) > 0 {

					openAIResultFileDocID := common.GenerateCombinedHashID(openAIResultFile, common.OPENAI_FILE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						openAIResultFile, openAIResultFileDocID, common.OPENAI_FILE_RESOURCE_TYPE, true)
				}
			}
		}

	case common.OPENAI_ASSISTANT_RESOURCE_TYPE:

		if openAIModelOrFineTunedModel, ok := entityJSONMap["model"].(string); ok && len(openAIModelOrFineTunedModel) > 0 {

			entityType := common.OPENAI_MODEL_RESOURCE_TYPE

			if strings.HasPrefix(openAIModelOrFineTunedModel, "ft:") {
				entityType = common.OPENAI_FINETUNEDMODEL_RESOURCE_TYPE
			}

			openAIModelOrFineTunedModel = `/projects/` + resourceContextDoc.Account + `/models/` + openAIModelOrFineTunedModel

			openAIModelOrFineTunedModelDocID := common.GenerateCombinedHashID(openAIModelOrFineTunedModel, entityType, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

			assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				openAIModelOrFineTunedModel, openAIModelOrFineTunedModelDocID, entityType, true)
		}

	case common.OPENAI_TUNINGJOB_RESOURCE_TYPE:

		if openAIFineTunedModel, ok := entityJSONMap["fine_tuned_model"].(string); ok && len(openAIFineTunedModel) > 0 {

			openAIFineTunedModel = `/projects/` + resourceContextDoc.Account + `/models/` + openAIFineTunedModel

			openAIFineTunedModelDocID := common.GenerateCombinedHashID(openAIFineTunedModel, common.OPENAI_FINETUNEDMODEL_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

			assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				openAIFineTunedModel, openAIFineTunedModelDocID, common.OPENAI_FINETUNEDMODEL_RESOURCE_TYPE, true)
		}

		if openAIModel, ok := entityJSONMap["model"].(string); ok && len(openAIModel) > 0 {

			openAIModel = `/projects/` + resourceContextDoc.Account + `/models/` + openAIModel

			openAIModelDocID := common.GenerateCombinedHashID(openAIModel, common.OPENAI_MODEL_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

			assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				openAIModel, openAIModelDocID, common.OPENAI_MODEL_RESOURCE_TYPE, true)
		}

		if openAITrainingFile, ok := entityJSONMap["training_file"].(string); ok && len(openAITrainingFile) > 0 {

			openAITrainingFileDocID := common.GenerateCombinedHashID(openAITrainingFile, common.OPENAI_FILE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

			assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				openAITrainingFile, openAITrainingFileDocID, common.OPENAI_FILE_RESOURCE_TYPE, true)
		}

		if openAIValidationFile, ok := entityJSONMap["validation_file"].(string); ok && len(openAIValidationFile) > 0 {

			openAIValidationFileDocID := common.GenerateCombinedHashID(openAIValidationFile, common.OPENAI_FILE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

			assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				openAIValidationFile, openAIValidationFileDocID, common.OPENAI_FILE_RESOURCE_TYPE, true)
		}

		if resultFiles, ok := entityJSONMap["result_files"].([]any); ok {

			for _, resultFile := range resultFiles {

				if openAIResultFile, ok := resultFile.(string); ok && len(openAIResultFile) > 0 {

					openAIResultFileDocID := common.GenerateCombinedHashID(openAIResultFile, common.OPENAI_FILE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						openAIResultFile, openAIResultFileDocID, common.OPENAI_FILE_RESOURCE_TYPE, true)
				}
			}
		}

		if checkPointModels, ok := entityJSONMap["checkpoint_models"].([]any); ok {

			for _, checkpointModel := range checkPointModels {

				if openAICheckPointModel, ok := checkpointModel.(string); ok && len(openAICheckPointModel) > 0 {

					openAICheckPointModel = `/projects/` + resourceContextDoc.Account + `/models/` + openAICheckPointModel

					openAICheckpointModelDocID := common.GenerateCombinedHashID(openAICheckPointModel, common.OPENAI_FINETUNEDMODEL_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

					assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
						openAICheckPointModel, openAICheckpointModelDocID, common.OPENAI_FINETUNEDMODEL_RESOURCE_TYPE, true)
				}
			}
		}

	case common.OPENAI_TUNINGJOBCHECKPOINT_RESOURCE_TYPE:

		if openAIFineTuningJob, ok := entityJSONMap["fine_tuning_job_id"].(string); ok && len(openAIFineTuningJob) > 0 {

			openAIFineTuningJobDocID := common.GenerateCombinedHashID(openAIFineTuningJob, common.OPENAI_TUNINGJOB_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

			assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				openAIFineTuningJob, openAIFineTuningJobDocID, common.OPENAI_TUNINGJOB_RESOURCE_TYPE, true)
		}

		if openAIFineTunedModel, ok := entityJSONMap["fine_tuned_model_checkpoint"].(string); ok && len(openAIFineTunedModel) > 0 {

			openAIFineTunedModel = `/projects/` + resourceContextDoc.Account + `/models/` + openAIFineTunedModel

			openAIFineTunedModelDocID := common.GenerateCombinedHashID(openAIFineTunedModel, common.OPENAI_FINETUNEDMODEL_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

			assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
				openAIFineTunedModel, openAIFineTunedModelDocID, common.OPENAI_FINETUNEDMODEL_RESOURCE_TYPE, true)
		}

	case common.OPENAI_APIKEY_RESOURCE_TYPE:

		if owner, ok := entityJSONMap["owner"].(map[string]any); ok {
			if ownerType, ok := owner["type"].(string); ok && ownerType == "service_account" {
				if serviceAccountMap, ok := owner["service_account"].(map[string]any); ok {
					if serviceAccountID, ok := serviceAccountMap["id"].(string); ok && len(serviceAccountID) > 0 {

						serviceAccountDocID := common.GenerateCombinedHashID(serviceAccountID, common.OPENAI_SERVICEACCOUNT_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

						assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
							serviceAccountID, serviceAccountDocID, common.OPENAI_SERVICEACCOUNT_RESOURCE_TYPE, true)
					}
				}
			}
		}

	default:

		if resourceContextDoc.ServiceID == common.GCP_SERVICE_ID_INT && strings.Contains(resourceContextDoc.ResourceType, "k8s.io") {

			assignRelatedResourcesFromSingleResourceCollector(resourceContext, resourceContextDoc, resourceDocID, entityJSONMap)

		} else if resourceContextDoc.ServiceID == common.AZURE_SERVICE_ID_INT && strings.Contains(resourceContextDoc.ResourceType, "K8s") {

			if clusterName, ok := entityJSONMap["clusterName"].(string); ok && len(clusterName) >= 0 {

				var clusterPrefix string

				splitName := strings.Split(resourceContextDoc.ResourceID, "/")
				for i, v := range splitName {
					if v == "managedclusters" {
						clusterPrefix = strings.Join(splitName[:i], "/")
						break
					}
				}

				if len(clusterPrefix) > 0 {

					clusterID := clusterPrefix + "/providers/microsoft.containerservice/managedclusters/" + strings.ToLower(clusterName)

					clusterDocID := common.GenerateCombinedHashID(clusterID, common.AZURE_AKSCLUSTER_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

					assignRelatedParentResourceOneWay(resourceContext, resourceDocID, clusterID, clusterDocID,
						common.AZURE_AKSCLUSTER_RESOURCE_TYPE, true)
				}
			}
		}
	}
}

func processFirewallRulesForTarget(resourceContext *ResourceContext,
	network string,
	targetKey string,
	resourceContextDoc common.ResourceContextInsertDoc,
	resourceDocID string) {

	firewallTargets, exists := resourceContext.GetGCPFirewallTargets(network)
	if !exists {
		return
	}

	firewallRuleIDs, ok := firewallTargets[targetKey]
	if !ok {
		return
	}

	for _, firewallRuleID := range firewallRuleIDs {
		firewallDoc, err := common.GetCloudResourceDocumentForEntityIDAndType(
			firewallRuleID,
			common.GCP_FIREWALL_RESOURCE_TYPE,
			resourceContextDoc.TenantID,
			resourceContext.LastCollectedAt)

		if err != nil {
			break
		}

		if accountID, ok := firewallDoc["accountId"].(string); ok && len(accountID) > 0 {
			firewallDocID := common.GenerateCombinedHashID(
				firewallRuleID,
				common.GCP_FIREWALL_RESOURCE_TYPE,
				accountID,
				resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

			assignRelatedResource(
				resourceContext,
				resourceContextDoc.ResourceID,
				resourceDocID,
				resourceContextDoc.ResourceType,
				firewallRuleID,
				firewallDocID,
				common.GCP_FIREWALL_RESOURCE_TYPE,
				true,
			)
		}
	}
}

func assignRelatedResource(resourceContext *ResourceContext, resourceID, resourceDocID, resourceType, relatedResourceID, relatedResourceDocID, relatedResourceType string, contextual bool) {
	relatedResources, exists := resourceContext.GetRelatedResourceList(resourceDocID)
	if !exists {
		relatedResources = []RelatedResource{}
	}

	relatedResources = append(
		relatedResources,
		RelatedResource{
			ResourceID:         relatedResourceID,
			ResourceType:       relatedResourceType,
			ResourceDocID:      relatedResourceDocID,
			ContextualRelation: contextual,
		},
	)
	resourceContext.SetRelatedResourceList(resourceDocID, relatedResources)

	reciprocalRelatedResources, exists := resourceContext.GetRelatedResourceList(relatedResourceDocID)
	if !exists {
		reciprocalRelatedResources = []RelatedResource{}
	}

	reciprocalRelatedResources = append(
		reciprocalRelatedResources,
		RelatedResource{
			ResourceID:         resourceID,
			ResourceType:       resourceType,
			ResourceDocID:      resourceDocID,
			ContextualRelation: contextual,
		},
	)
	resourceContext.SetRelatedResourceList(relatedResourceDocID, reciprocalRelatedResources)
}

func assignRelatedResourcePriority(resourceContext *ResourceContext, resourceID, resourceDocID, resourceType, relatedResourceID, relatedResourceDocID, relatedResourceType string, contextual bool) {
	relatedResources, exists := resourceContext.GetRelatedResourceList(resourceDocID)
	if !exists {
		relatedResources = []RelatedResource{}
	}

	relatedResources = append(
		relatedResources,
		RelatedResource{
			ResourceID:         relatedResourceID,
			ResourceType:       relatedResourceType,
			ResourceDocID:      relatedResourceDocID,
			ContextualRelation: contextual,
			Priority:           true,
		},
	)
	resourceContext.SetRelatedResourceList(resourceDocID, relatedResources)

	reciprocalRelatedResources, exists := resourceContext.GetRelatedResourceList(relatedResourceDocID)
	if !exists {
		reciprocalRelatedResources = []RelatedResource{}
	}

	reciprocalRelatedResources = append(
		reciprocalRelatedResources,
		RelatedResource{
			ResourceID:         resourceID,
			ResourceType:       resourceType,
			ResourceDocID:      resourceDocID,
			ContextualRelation: contextual,
			Priority:           true,
		},
	)
	resourceContext.SetRelatedResourceList(relatedResourceDocID, reciprocalRelatedResources)
}

func assignRelatedParentResource(resourceContext *ResourceContext, resourceID, resourceDocID, resourceType, relatedResourceID, relatedResourceDocID, relatedResourceType string, contextual bool) {

	relatedResources, exists := resourceContext.GetRelatedResourceList(resourceDocID)
	if !exists {
		relatedResources = []RelatedResource{}
	}

	relatedResources = append(
		relatedResources,
		RelatedResource{
			ResourceID:         relatedResourceID,
			ResourceType:       relatedResourceType,
			ResourceDocID:      relatedResourceDocID,
			ContextualRelation: contextual,
			Parent:             true,
		},
	)
	resourceContext.SetRelatedResourceList(resourceDocID, relatedResources)

	reciprocalRelatedResources, exists := resourceContext.GetRelatedResourceList(relatedResourceDocID)
	if !exists {
		reciprocalRelatedResources = []RelatedResource{}
	}

	reciprocalRelatedResources = append(
		reciprocalRelatedResources,
		RelatedResource{
			ResourceID:         resourceID,
			ResourceType:       resourceType,
			ResourceDocID:      resourceDocID,
			ContextualRelation: contextual,
		},
	)
	resourceContext.SetRelatedResourceList(relatedResourceDocID, reciprocalRelatedResources)
}

func assignRelatedParentResourceOneWay(resourceContext *ResourceContext, resourceDocID, relatedResourceID, relatedResourceDocID, relatedResourceType string, contextual bool) {

	relatedResources, exists := resourceContext.GetRelatedResourceList(resourceDocID)
	if !exists {
		relatedResources = []RelatedResource{}
	}

	relatedResources = append(
		relatedResources,
		RelatedResource{
			ResourceID:         relatedResourceID,
			ResourceType:       relatedResourceType,
			ResourceDocID:      relatedResourceDocID,
			ContextualRelation: contextual,
			Parent:             true,
		},
	)
	resourceContext.SetRelatedResourceList(resourceDocID, relatedResources)
}

func assignRelatedResourceWithTransitive(resourceContext *ResourceContext, resourceID, resourceDocID, resourceType, relatedResourceID, relatedResourceDocID, relatedResourceType, transitiveResourceType string, contextual bool) {

	relatedResources, exists := resourceContext.GetRelatedResourceList(resourceDocID)
	if !exists {
		relatedResources = []RelatedResource{}
	}

	relatedResources = append(
		relatedResources,
		RelatedResource{
			ResourceID:             relatedResourceID,
			ResourceType:           relatedResourceType,
			ResourceDocID:          relatedResourceDocID,
			ContextualRelation:     contextual,
			TransitiveResourceType: transitiveResourceType,
		},
	)
	resourceContext.SetRelatedResourceList(resourceDocID, relatedResources)

	reciprocalRelatedResources, exists := resourceContext.GetRelatedResourceList(relatedResourceDocID)
	if !exists {
		reciprocalRelatedResources = []RelatedResource{}
	}

	reciprocalRelatedResources = append(
		reciprocalRelatedResources,
		RelatedResource{
			ResourceID:         resourceID,
			ResourceType:       resourceType,
			ResourceDocID:      resourceDocID,
			ContextualRelation: contextual,
		},
	)
	resourceContext.SetRelatedResourceList(relatedResourceDocID, reciprocalRelatedResources)
}

func assignRelatedResourcesFromSingleResourceCollector(resourceContext *ResourceContext, resourceContextDoc common.ResourceContextInsertDoc, resourceDocID string, entityJSONMap map[string]any) {
	if k8sParentAssetType, ok := entityJSONMap["parentAssetType"].(string); ok && len(k8sParentAssetType) > 0 {

		k8sParentType := k8sParentAssetType

		k8sParentFullName, _ := entityJSONMap["parentFullResourceName"].(string)

		var k8sParentID string

		splitName := strings.Split(k8sParentFullName, "/")
		for i, v := range splitName {
			if v == "projects" {
				k8sParentID = strings.Join(splitName[i:], "/")
				break
			}
		}

		switch k8sParentType {
		case "rbac.authorization.k8s.io/ClusterRole":
			k8sParentType = common.GCP_GKECLUSTERROLE_RESOURCE_TYPE
		case "rbac.authorization.k8s.io/Role":
			k8sParentType = common.GCP_GKENAMESPACEROLE_RESOURCE_TYPE
		case "k8s.io/Namespace":
			k8sParentType = common.GCP_GKENAMESPACE_RESOURCE_TYPE
		case "k8s.io/ServiceAccount":
			k8sParentType = common.GCP_GKESERVICEACCOUNT_RESOURCE_TYPE
		case "apps.k8s.io/DaemonSet":
			k8sParentType = common.GCP_GKEDAEMONSET_RESOURCE_TYPE
		case "apps.k8s.io/ReplicaSet":
			k8sParentType = common.GCP_GKEREPLICASET_RESOURCE_TYPE
		case "container.googleapis.com/Cluster":
			k8sParentType = common.GCP_GKECLUSTER_RESOURCE_TYPE
		}

		if len(k8sParentID) > 0 {

			k8sParentDocID := common.GenerateCombinedHashID(k8sParentID, k8sParentType, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)
			assignRelatedParentResourceOneWay(resourceContext, resourceDocID, k8sParentID,
				k8sParentDocID, k8sParentType, true)
		}

		if resourceContextDoc.ResourceType == common.GCP_GKENODE_RESOURCE_TYPE {

			var instancePrefix string

			splitName := strings.Split(resourceContextDoc.ResourceID, "/")
			for i, v := range splitName {
				if v == "clusters" {
					instancePrefix = strings.Join(splitName[:i], "/")
					break
				}
			}

			if len(instancePrefix) > 0 {

				instanceID := instancePrefix + "/instances/" + strings.ToLower(splitName[len(splitName)-1])

				instanceDocID := common.GenerateCombinedHashID(instanceID, common.GCP_INSTANCE_RESOURCE_TYPE, resourceContextDoc.Account, resourceContext.LastCollectedAt, resourceContextDoc.TenantID)

				assignRelatedResource(resourceContext, resourceContextDoc.ResourceID, resourceDocID, resourceContextDoc.ResourceType,
					instanceID, instanceDocID, common.GCP_INSTANCE_RESOURCE_TYPE, true)
			}
		}
	}
}
