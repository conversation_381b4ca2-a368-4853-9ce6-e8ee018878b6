package context

import (
	"bytes"
	"compress/gzip"
	"encoding/base64"
	"encoding/json"
	"io"
	"path/filepath"
	"regexp"
	"strings"
	"unicode"

	"github.com/precize/common"
	"github.com/precize/logger"
)

func isDataResourceType(resourceType string) bool {

	switch resourceType {
	case common.AWS_EC2_RESOURCE_TYPE, common.AWS_EBSVOLUME_RESOURCE_TYPE, common.AWS_EBSSNAPSHOT_RESOURCE_TYPE,
		common.AWS_S3_RESOURCE_TYPE, common.AWS_AMI_RESOURCE_TYPE, common.AWS_RDS_RESOURCE_TYPE, common.AWS_RDSSNAPSHOT_RESOURCE_TYPE,
		common.AWS_RDSCLUSTERSNAPSHOT_RESOURCE_TYPE, common.AWS_DYNAMODB_RESOURCE_TYPE, common.AWS_ELASTICACHE_RESOURCE_TYPE,
		common.AWS_RDSCLUSTER_RESOURCE_TYPE, common.AWS_ELASTICSEARCH_RESOURCE_TYPE, common.AWS_NEPTUNECLUSTER_RESOURCE_TYPE,
		common.AWS_NEPTUNEINSTANCE_RESOURCE_TYPE, common.AWS_TIMESTREAMDB_RESOURCE_TYPE, common.AWS_TIMESTREAMTABLE_RESOURCE_TYPE,
		common.AZURE_VM_RESOURCE_TYPE, common.AZURE_VMDISK_RESOURCE_TYPE, common.AZURE_SNAPSHOT_RESOURCE_TYPE,
		common.AZURE_STORAGEACCOUNT_RESOURCE_TYPE, common.AZURE_MYSQL_RESOURCE_TYPE, common.AZURE_SQLDB_RESOURCE_TYPE,
		common.AZURE_SQLSERVER_RESOURCE_TYPE, common.AZURE_COSMOSDB_RESOURCE_TYPE, common.AZURE_DATABRICKWORKSPACE_RESOURCE_TYPE,
		common.AZURE_KEYVAULT_RESOURCE_TYPE, common.AZURE_MARIADB_RESOURCE_TYPE, common.AZURE_POSTGRES_RESOURCE_TYPE,
		common.AZURE_REDISCACHE_RESOURCE_TYPE, common.AZURE_RG_RESOURCE_TYPE, common.GCP_INSTANCE_RESOURCE_TYPE, common.GCP_GKENODE_RESOURCE_TYPE,
		common.GCP_GKECLUSTER_RESOURCE_TYPE, common.GCP_VMDISK_RESOURCE_TYPE, common.GCP_DISK_SNAPSHOT_RESOURCE_TYPE,
		common.GCP_CLOUDSTORAGE_RESOURCE_TYPE, common.GCP_SQLDB_RESOURCE_TYPE, common.GCP_BIGQUERYTABLE_RESOURCE_TYPE,
		common.GCP_BIGQUERYDATASET_RESOURCE_TYPE, common.GCP_BIGTABLE_RESOURCE_TYPE, common.GCP_BIGTABLECLUSTER_RESOURCE_TYPE,
		common.GCP_SPANNERDB_RESOURCE_TYPE, common.GCP_SPANNERDBINSTANCE_RESOURCE_TYPE, common.GCP_DATAPROCCLUSTER_RESOURCE_TYPE,
		common.GCP_DATAFLOWJOB_RESOURCE_TYPE, common.GCP_KMS_RESOURCE_TYPE:

		return true
	}

	return false
}

func IsDescriptionKey(tagKey string) bool {

	if _, ok := descriptionTagKeys[strings.ToLower(tagKey)]; ok {
		return true
	}

	for key := range descriptionTagKeys {
		if strings.Contains(strings.ToLower(tagKey), key) {
			return true
		}
	}
	return false
}

func getResourceInbuiltProperty(resourceContext *ResourceContext, entityJSON map[string]any,
	resourceContextInsertDoc *common.ResourceContextInsertDoc) {

	switch resourceContextInsertDoc.ResourceType {

	case common.AWS_ACCOUNT_RESOURCE_TYPE:
		if organization, ok := entityJSON["organization"].(map[string]any); ok {
			if owner, ok := organization["masterAccountEmail"].(string); ok && len(owner) > 0 {
				resourceContextInsertDoc.ResourceOwnerTypes.InheritedOwners = append(
					resourceContextInsertDoc.ResourceOwnerTypes.InheritedOwners,
					resourceContext.GetUserContextItem(owner, common.ORG_ACCOUNT_OWNER_USER_TYPE, "", "", nil),
				)
			}
		}

	case common.AWS_S3_RESOURCE_TYPE:

		if owner, ok := entityJSON["bucketOwner"].(string); ok && len(owner) > 0 && owner != "null" {
			resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
				resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
				resourceContext.GetUserContextItem(owner, common.RESOURCE_OWNER_USER_TYPE, "", "", nil),
			)
		}

	case common.AWS_LAMBDA_RESOURCE_TYPE:
		if funcConfig, ok := entityJSON["functionConfiguration"].(map[string]any); ok {
			if env, ok := funcConfig["environment"].(map[string]any); ok && len(env) > 0 {
				if variables, ok := env["variables"].(map[string]any); ok && len(variables) > 0 {

					for key, value := range variables {
						if key == "KEEPER_USER" {

							if email, ok := value.(string); ok {

								// Format: "<user>+vaultadmin@<domain>",
								atIndex := strings.LastIndex(email, "@")
								if atIndex > 0 {
									username := email[:atIndex]
									domain := email[atIndex:]

									plusIndex := strings.Index(username, "+")
									if plusIndex > 0 {
										email = username[:plusIndex] + domain
									}
								}

								resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
									resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
									resourceContext.GetUserContextItem(email, common.RESOURCE_OWNER_USER_TYPE, "", "", nil),
								)
							}
						}
					}

				}
			}
		}

	case common.AWS_EC2_RESOURCE_TYPE:

		if rightSizingRecommendation, ok := entityJSON["rightsizingRecommendation"].(map[string]any); ok {
			if currentInstance, ok := rightSizingRecommendation["currentInstance"].(map[string]any); ok {
				if tags, ok := currentInstance["tags"].([]any); ok {
					for _, tag := range tags {
						if tagMap, ok := tag.(map[string]any); ok {
							if key, ok := tagMap["key"].(string); ok {

								if key == "aws:createdBy" || key == "aws:modifiedBy" || key == "aws:updatedBy" {
									if values, ok := tagMap["values"].([]any); ok {

										for _, value := range values {
											if valueString, ok := value.(string); ok {
												// Format is typically "AssumedRole:<Role Id>:<User>" or "User:<user>"
												parts := strings.Split(valueString, ":")
												username := parts[len(parts)-1]

												resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
													resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
													resourceContext.GetUserContextItem(username, common.RESOURCE_OWNER_USER_TYPE, "", "", nil),
												)
											}
										}
									}
								}
							}
						}
					}
				}

			}
		}

		if instanceProperties, ok := entityJSON["instance"].(map[string]any); ok {
			if clientToken, ok := instanceProperties["clientToken"].(string); ok && len(clientToken) > 0 && strings.Contains(strings.ToLower(clientToken), "terraform") {

				resourceContextInsertDoc.ResourceDeploymentTypes.DerivedDeployment = append(
					resourceContextInsertDoc.ResourceDeploymentTypes.DerivedDeployment,
					common.ResourceContextItem{
						Name: TERRAFORM_DEPLOYMENT,
						Type: common.RESOURCEPROPERTY_DEPLOYMENT_TYPE,
					},
				)
			}

		}

	case common.AZURE_TENANT_RESOURCE_TYPE:
		if aDInfo, ok := entityJSON["activeDirectoryInfo"].(map[string]any); ok && len(aDInfo) > 0 {
			if contactInfo, ok := aDInfo["technicalNotificationMails"].([]string); ok && len(contactInfo) > 0 {
				for _, email := range contactInfo {
					resourceContextInsertDoc.ResourceOwnerTypes.OpsOwners = append(
						resourceContextInsertDoc.ResourceOwnerTypes.OpsOwners,
						resourceContext.GetUserContextItem(email, common.RESOURCE_OPS_CONTACT_USER_TYPE, "", "", nil),
					)
				}
			}
		}

	case common.AZURE_RG_RESOURCE_TYPE:

		if policies, ok := entityJSON["policies"].(map[string]any); ok {

			if systemData, ok := policies["systemData"].(map[string]any); ok {

				if lastModifiedBy, ok := systemData["lastModifiedBy"].(string); ok {
					if lastModifiedByType, ok := systemData["lastModifiedByType"].(string); ok {
						if len(lastModifiedBy) > 0 {
							userType := common.RESOURCE_OWNER_USER_TYPE
							identityID := ""
							if lastModifiedByType == "Application" {
								identityID = lastModifiedBy
								lastModifiedBy = getAzureAppName(resourceContext, lastModifiedBy, "") + APP_USER_SUFFIX
								userType = common.RESOURCE_OWNERAPP_USER_TYPE
							}
							resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
								resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
								resourceContext.GetUserContextItem(lastModifiedBy, userType, "", identityID, nil),
							)
						}
					}
				}

				if createdBy, ok := systemData["createdBy"].(string); ok {
					if createdByType, ok := systemData["createdByType"].(string); ok {
						if len(createdBy) > 0 {
							userType := common.RESOURCE_OWNER_USER_TYPE
							identityID := ""
							if createdByType == "Application" {
								identityID = createdBy
								createdBy = getAzureAppName(resourceContext, createdBy, "") + APP_USER_SUFFIX
								userType = common.RESOURCE_OWNERAPP_USER_TYPE
							}
							resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
								resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
								resourceContext.GetUserContextItem(createdBy, userType, "", identityID, nil),
							)
						}
					}
				}
			}
		}

	case common.AZURE_GRAPHAPP_RESOURCE_TYPE:

		if owners, ok := entityJSON["owners"].([]any); ok {
			for _, owner := range owners {
				if ownerMap, ok := owner.(map[string]any); ok {
					if userPrincipalName, ok := ownerMap["userPrincipalName"].(string); ok && len(userPrincipalName) > 0 {
						resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners = append(
							resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners,
							resourceContext.GetUserContextItem(userPrincipalName, common.RESOURCE_OWNER_USER_TYPE, "", "", nil),
						)
					}
				}
			}
		}

	case common.AZURE_MLWORKSPACEDEPLOYMENTS_RESOURCE_TYPE, common.AZURE_CONTAINERREGISTRY_RESOURCE_TYPE:

		if systemData, ok := entityJSON["systemData"].(map[string]any); ok {

			if lastModifiedBy, ok := systemData["lastModifiedBy"].(string); ok && len(lastModifiedBy) > 0 {
				resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
					resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
					resourceContext.GetUserContextItem(lastModifiedBy, common.RESOURCE_OWNER_USER_TYPE, "", "", nil),
				)
			}

			if createdBy, ok := systemData["createdBy"].(string); ok && len(createdBy) > 0 {
				resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
					resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
					resourceContext.GetUserContextItem(createdBy, common.RESOURCE_OWNER_USER_TYPE, "", "", nil),
				)
			}
		}

	case common.AZURE_OPENAIMODEL_RESOURCE_TYPE:

		if model, ok := entityJSON["model"].(map[string]any); ok {

			if systemData, ok := model["systemData"].(map[string]any); ok {

				if lastModifiedBy, ok := systemData["lastModifiedBy"].(string); ok {
					if lastModifiedByType, ok := systemData["lastModifiedByType"].(string); ok {
						if len(lastModifiedBy) > 0 {
							userType := common.RESOURCE_OWNER_USER_TYPE
							identityID := ""
							if lastModifiedByType == "Application" {
								identityID = lastModifiedBy
								lastModifiedBy = getAzureAppName(resourceContext, lastModifiedBy, "") + APP_USER_SUFFIX
								userType = common.RESOURCE_OWNERAPP_USER_TYPE
							}
							resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
								resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
								resourceContext.GetUserContextItem(lastModifiedBy, userType, "", identityID, nil),
							)
						}
					}
				}

				if createdBy, ok := systemData["createdBy"].(string); ok {
					if createdByType, ok := systemData["createdByType"].(string); ok {
						if len(createdBy) > 0 {
							userType := common.RESOURCE_OWNER_USER_TYPE
							identityID := ""
							if createdByType == "Application" {
								identityID = createdBy
								createdBy = getAzureAppName(resourceContext, createdBy, "") + APP_USER_SUFFIX
								userType = common.RESOURCE_OWNERAPP_USER_TYPE
							}
							resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
								resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
								resourceContext.GetUserContextItem(createdBy, userType, "", identityID, nil),
							)
						}
					}
				}
			}
		}

	case common.AZURE_MLWORKSPACEMODELS_RESOURCE_TYPE:

		if lastModifiedBy, ok := entityJSON["lastModifiedBy"].(map[string]any); ok {
			if userName, ok := lastModifiedBy["userName"].(string); ok && len(userName) > 0 {
				resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
					resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
					resourceContext.GetUserContextItem(userName, common.RESOURCE_OWNER_USER_TYPE, "", "", nil),
				)
			}
		}

		if createdBy, ok := entityJSON["createdBy"].(map[string]any); ok {
			if userName, ok := createdBy["userName"].(string); ok && len(userName) > 0 {
				resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
					resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
					resourceContext.GetUserContextItem(userName, common.RESOURCE_OWNER_USER_TYPE, "", "", nil),
				)
			}
		}

	case common.AZURE_SPEECHSERVICE_RESOURCE_TYPE:

		if lastModifiedBy, ok := entityJSON["lastModifiedBy"].(string); ok && len(lastModifiedBy) > 0 {
			resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
				resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
				resourceContext.GetUserContextItem(lastModifiedBy, common.RESOURCE_OWNER_USER_TYPE, "", "", nil),
			)
		}

		if createdBy, ok := entityJSON["createdBy"].(string); ok && len(createdBy) > 0 {
			resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
				resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
				resourceContext.GetUserContextItem(createdBy, common.RESOURCE_OWNER_USER_TYPE, "", "", nil),
			)
		}

	case common.AZURE_MGMTGRP_RESOURCE_TYPE:
		if updatedBy, ok := entityJSON["updatedBy"].(string); ok {
			resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
				resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
				resourceContext.GetUserContextItem(updatedBy, common.RESOURCE_OWNER_USER_TYPE, "", "", nil),
			)
		}

	case common.GCP_BIGQUERYTABLE_RESOURCE_TYPE:

		if accesses, ok := entityJSON["access"].([]any); ok {
			for _, access := range accesses {
				if accessMap, ok := access.(map[string]any); ok {
					if userEmail, ok := accessMap["userByEmail"].(string); ok && len(userEmail) > 0 {
						resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners = append(
							resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners,
							resourceContext.GetUserContextItem(userEmail, common.RESOURCE_OWNER_USER_TYPE, "", "", nil),
						)
					}
				}
			}
		}

	case common.GCP_BIGQUERYDATASET_RESOURCE_TYPE:

		if accesses, ok := entityJSON["access"].([]any); ok {
			for _, access := range accesses {
				if accessMap, ok := access.(map[string]any); ok {
					if userEmail, ok := accessMap["userByEmail"].(string); ok && len(userEmail) > 0 && !strings.HasSuffix(userEmail, ".gserviceaccount.com") {
						resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners = append(
							resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners,
							resourceContext.GetUserContextItem(userEmail, common.RESOURCE_OWNER_USER_TYPE, "", "", nil),
						)
					}
				}
			}
		}

	case common.GCP_NOTEBOOKRUNTIME_RESOURCE_TYPE:

		if runtimeUser, ok := entityJSON["runtimeUser"].(string); ok && len(runtimeUser) > 0 {
			resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners = append(
				resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners,
				resourceContext.GetUserContextItem(runtimeUser, common.RESOURCE_OWNER_USER_TYPE, "", "", nil),
			)
		}

	case common.OPENAI_APIKEY_RESOURCE_TYPE:
		if owner, ok := entityJSON["owner"].(map[string]any); ok {
			if ownerType, ok := owner["type"].(string); ok && ownerType == "user" {
				if userMap, ok := owner["user"].(map[string]any); ok {
					user, _ := userMap["email"].(string)
					if len(user) <= 0 {
						user, _ = userMap["name"].(string)
					}
					resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
						resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
						resourceContext.GetUserContextItem(user, common.RESOURCE_OWNER_USER_TYPE, "", "", nil),
					)
				}
			}
		}

	case common.APP_RESOURCE_TYPE, common.AWS_AISAGEMAKERMODELTYPE_RESOURCE_TYPE:

		owner := formCompleteEmailFormat("Precize Support", "<EMAIL>")

		resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners = append(
			resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners,
			resourceContext.GetUserContextItem(owner, common.PRECIZE_DETECTED_USER_TYPE, "", "", nil),
		)

	case common.AZURE_ADUSER_RESOURCE_TYPE, common.AWS_IAM_USER_RESOURCE_TYPE, common.AWS_SSOUSER_RESOURCE_TYPE, common.GCP_IAM_RESOURCE_TYPE, common.AWS_ROOTUSER_RESOURCE_TYPE:
		if hasConsoleLogin, ok := entityJSON["hasLoginProfile"].(bool); ok {
			resourceContextInsertDoc.HasConsoleLogin = hasConsoleLogin
		}

	case common.AZURE_DEVICE_RESOURCE_TYPE:

		registeredDeviceEmail := make(map[string]struct{})

		if registeredOwners, ok := entityJSON["registeredOwners"].([]any); ok {
			for _, registeredOwner := range registeredOwners {
				if registeredOwnerMap, ok := registeredOwner.(map[string]any); ok {
					if userPrincipal, ok := registeredOwnerMap["userPrincipalName"].(string); ok {
						registeredDeviceEmail[userPrincipal] = struct{}{}
						resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners = append(
							resourceContextInsertDoc.ResourceOwnerTypes.DefinedOwners,
							resourceContext.GetUserContextItem(userPrincipal, common.RESOURCE_OWNER_USER_TYPE, "", "", nil),
						)
					}
				}
			}
		}

		if registeredUsers, ok := entityJSON["registeredUsers"].([]any); ok {
			for _, registeredUser := range registeredUsers {
				if registeredUserMap, ok := registeredUser.(map[string]any); ok {
					if userPrincipal, ok := registeredUserMap["userPrincipalName"].(string); ok {
						if _, ok := registeredDeviceEmail[userPrincipal]; !ok {
							resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
								resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
								resourceContext.GetUserContextItem(userPrincipal, common.RESOURCE_OWNER_USER_TYPE, "", "", nil),
							)
						}
					}
				}
			}
		}

	case common.AWS_GLUECATALOGTABLE_RESOURCE_TYPE:

		if createdBy, ok := entityJSON["createdBy"].(string); ok && len(createdBy) > 0 {

			if strings.Contains(createdBy, "assumed-role/") {
				splitString := strings.Split(createdBy, "/")
				if len(splitString) >= 2 {
					iamRole := strings.ToLower(splitString[len(splitString)-2])

					resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
						resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
						resourceContext.GetUserContextItem(iamRole+IAM_ROLE_SUFFIX, common.RESOURCE_OWNER_USER_TYPE, "", iamRole, nil),
					)
				}
			}
		}

	default:

		if resourceContextInsertDoc.ServiceID == common.AZURE_SERVICE_ID_INT {

			if systemData, ok := entityJSON["systemData"].(map[string]any); ok {

				if lastModifiedBy, ok := systemData["lastModifiedBy"].(string); ok {
					userType := common.RESOURCE_OWNER_USER_TYPE
					if lastModifiedByType, ok := systemData["lastModifiedByType"].(string); ok {
						if len(lastModifiedBy) > 0 {
							identityID := ""
							if lastModifiedByType == "Application" {
								identityID = lastModifiedBy
								lastModifiedBy = getAzureAppName(resourceContext, lastModifiedBy, "") + APP_USER_SUFFIX
								userType = common.RESOURCE_OWNERAPP_USER_TYPE
							}
							resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
								resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
								resourceContext.GetUserContextItem(lastModifiedBy, userType, "", identityID, nil),
							)
						}
					} else {
						resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
							resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
							resourceContext.GetUserContextItem(lastModifiedBy, userType, "", "", nil),
						)
					}
				}

				if createdBy, ok := systemData["createdBy"].(string); ok {
					userType := common.RESOURCE_OWNER_USER_TYPE
					if createdByType, ok := systemData["createdByType"].(string); ok {
						if len(createdBy) > 0 {
							identityID := ""
							if createdByType == "Application" {
								identityID = createdBy
								createdBy = getAzureAppName(resourceContext, createdBy, "") + APP_USER_SUFFIX
								userType = common.RESOURCE_OWNERAPP_USER_TYPE
							}
							resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
								resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
								resourceContext.GetUserContextItem(createdBy, userType, "", identityID, nil),
							)
						}
					} else {
						resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
							resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
							resourceContext.GetUserContextItem(createdBy, userType, "", "", nil),
						)
					}
				}
			}
		}

		description := ""
		switch resourceContextInsertDoc.ResourceType {
		case common.AWS_IAM_ROLE_RESOURCE_TYPE:
			if role, ok := entityJSON["role"].(map[string]any); ok {
				if desc, ok := role["description"].(string); ok && len(desc) > 0 {
					description = strings.ToLower(desc)
				}
			}
		default:
			if desc, ok := entityJSON["description"].(string); ok && len(desc) > 0 {
				description = strings.ToLower(desc)
			}
		}

		if len(description) > 0 {
			getContextFromDescription(description, resourceContext, resourceContextInsertDoc)
		}
	}
}

func getContextFromDescription(description string, resourceContext *ResourceContext, resourceContextInsertDoc *common.ResourceContextInsertDoc) {

	var (
		uniqueOwners        = make(map[string]struct{})
		uniqueDeployments   = make(map[string]struct{})
		uniqueSoftwares     = make(map[string]struct{})
		uniqueApps          = make(map[string]struct{})
		uniqueCompliances   = make(map[string]struct{})
		uniqueSensitivities = make(map[string]struct{})
		uniqueTeams         = make(map[string]struct{})
	)

	if softwareNames := GetSoftwareNameListFromValue(description); len(softwareNames) > 0 {

		for _, softwareName := range softwareNames {
			if _, ok := uniqueSoftwares[softwareName]; !ok {

				uniqueSoftwares[softwareName] = struct{}{}

				resourceContextInsertDoc.ResourceSoftwareTypes.DerivedSoftware = append(
					resourceContextInsertDoc.ResourceSoftwareTypes.DerivedSoftware,
					common.ResourceContextItem{
						Name: softwareName,
						Type: common.DESC_SOFTWARE_TYPE,
					},
				)
			}
		}
	}

	if appNames := GetAppNameListFromValue(description, WithResourceType(resourceContextInsertDoc.ResourceType)); len(appNames) > 0 {

		for _, appName := range appNames {
			if _, ok := uniqueApps[appName]; !ok {

				uniqueApps[appName] = struct{}{}

				resourceContextInsertDoc.ResourceAppTypes.DerivedApp = append(
					resourceContextInsertDoc.ResourceAppTypes.DerivedApp,
					common.ResourceContextItem{
						Name: appName,
						Type: common.DESC_APP_TYPE,
					},
				)
			}
		}
	}

	if teamNames := GetTeamNameListFromValue(description); len(teamNames) > 0 {

		for _, teamName := range teamNames {
			if _, ok := uniqueTeams[teamName]; !ok {

				uniqueTeams[teamName] = struct{}{}

				resourceContextInsertDoc.ResourceTeamTypes.DerivedTeam = append(
					resourceContextInsertDoc.ResourceTeamTypes.DerivedTeam,
					common.ResourceContextItem{
						Name: teamName,
						Type: common.DESC_TEAM_TYPE,
					},
				)
			}
		}
	}

	if deploymentNames := GetDeploymentNamesFromValue(description); len(deploymentNames) > 0 {

		for _, deploymentName := range deploymentNames {
			if _, ok := uniqueDeployments[deploymentName]; !ok {

				uniqueDeployments[deploymentName] = struct{}{}

				resourceContextInsertDoc.ResourceDeploymentTypes.DerivedDeployment = append(
					resourceContextInsertDoc.ResourceDeploymentTypes.DerivedDeployment,
					common.ResourceContextItem{
						Name: deploymentName,
						Type: common.DESC_DEPLOYMENT_TYPE,
					},
				)
			}
		}
	}

	if sensitivityNames := GetSensitivityNameListFromValue(description); len(sensitivityNames) > 0 {

		for _, sensitivityName := range sensitivityNames {
			if _, ok := uniqueSensitivities[sensitivityName]; !ok {

				uniqueSensitivities[sensitivityName] = struct{}{}

				resourceContextInsertDoc.ResourceSensitivityTypes.DerivedSensitivity = append(
					resourceContextInsertDoc.ResourceSensitivityTypes.DerivedSensitivity,
					common.ResourceContextItem{
						Name: sensitivityName,
						Type: common.DESC_SENSITIVITY_TYPE,
					},
				)
			}
		}
	}

	if complianceNames := GetComplianceNameListFromValue(description); len(complianceNames) > 0 {

		for _, complianceName := range complianceNames {
			if _, ok := uniqueCompliances[complianceName]; !ok {

				uniqueCompliances[complianceName] = struct{}{}

				resourceContextInsertDoc.ResourceComplianceTypes.DerivedCompliance = append(
					resourceContextInsertDoc.ResourceComplianceTypes.DerivedCompliance,
					common.ResourceContextItem{
						Name: complianceName,
						Type: common.DESC_COMPLIANCE_TYPE,
					},
				)
			}
		}
	}

	users := separateEmailsFromDescription(description)

	names := getNamesFromDescription(description, resourceContext.TenantID)

	names = removeNamesOfTaggedEmails(names, users)
	users = append(users, names...)

	for _, user := range users {
		if _, ok := uniqueOwners[DERIVED_OWNER+user]; !ok {

			uniqueOwners[DERIVED_OWNER+user] = struct{}{}

			resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
				resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners,
				resourceContext.GetUserContextItem(user, common.DESC_USER_TYPE, "User has been derived from resource description", "", nil),
			)
		}
	}
}

func getNameForAWSAccount(entityJSON string) (accountName string) {

	entityJSONMap := make(map[string]any)

	if err := json.Unmarshal([]byte(entityJSON), &entityJSONMap); err != nil {
		return
	}

	if contactInformation, ok := entityJSONMap["contactInformation"].(map[string]any); ok {
		accountName, _ = contactInformation["fullName"].(string)
	}

	return
}

func getNameForAWSResource(resourceID, resourceType, resourceNameTagValue, entityJSON string) string {

	if len(resourceNameTagValue) > 0 {
		return resourceNameTagValue
	}

	switch resourceType {
	case common.AWS_S3_RESOURCE_TYPE:
		entityJSONMap := make(map[string]any)
		if err := json.Unmarshal([]byte(entityJSON), &entityJSONMap); err != nil {
			break
		}

		resourceName, _ := entityJSONMap["bucketName"].(string)
		return resourceName

	case common.AWS_DYNAMODB_RESOURCE_TYPE:
		entityJSONMap := make(map[string]any)
		if err := json.Unmarshal([]byte(entityJSON), &entityJSONMap); err != nil {
			break
		}

		if tableAttr, ok := entityJSONMap["tableDescription"].(map[string]any); ok {
			if tableName, ok := tableAttr["tableName"].(string); ok {
				return tableName
			}
		}
	}

	if entityJSONName := getResourceNameFromEntityJSON(entityJSON); len(entityJSONName) > 0 {
		return entityJSONName
	}

	if strings.HasPrefix(resourceID, "arn:") {
		splitString := strings.Split(resourceID, "/")
		resourceID = splitString[len(splitString)-1]
	}

	awsResourceIDRegex := regexp.MustCompile(`\b(i|vol|snap|eni|subnet|vpc|sg|acl|igw|eipalloc|eipassoc|rtb|nat|ami|cgw|dopt|dhcp|tgw|tgw-attach|tgw-route|tgw-rtb|tgw-vpn|vpn|vpn-conn|vgw|role|user)-[0-9a-f]{8,}\b`)

	if awsResourceIDRegex.MatchString(resourceID) {
		return ""
	}

	return resourceID
}

func getNameForAzureResource(resourceID, resourceType, entityJSON string) string {

	switch resourceType {
	case common.AZURE_SUBSCRIPTION_RESOURCE_TYPE, common.AZURE_GRAPHAPP_RESOURCE_TYPE:
		return getResourceNameFromEntityJSON(entityJSON)
	case common.AZURE_MGMTGRP_RESOURCE_TYPE:

		entityJSONMap := make(map[string]any)
		if err := json.Unmarshal([]byte(entityJSON), &entityJSONMap); err != nil {
			break
		}

		if properties, ok := entityJSONMap["properties"].(map[string]any); ok {
			resourceName, _ := properties["displayName"].(string)
			return resourceName
		}

	case common.AZURE_ADUSER_RESOURCE_TYPE:

		entityJSONMap := make(map[string]any)
		if err := json.Unmarshal([]byte(entityJSON), &entityJSONMap); err != nil {
			break
		}
		resourceName, _ := entityJSONMap["userPrincipalName"].(string)
		return resourceName
	}

	if entityJSONName := getResourceNameFromEntityJSON(entityJSON); len(entityJSONName) > 0 {
		return entityJSONName
	}

	return filepath.Base(resourceID)
}

func getNameForGCPResource(resourceID, resourceType, entityJSON string) string {

	switch resourceType {
	case common.GCP_SERVICEACCOUNT_RESOURCE_TYPE:
		entityJSONMap := make(map[string]any)

		if err := json.Unmarshal([]byte(entityJSON), &entityJSONMap); err != nil {
			break
		}

		if entityJsonDetails, ok := entityJSONMap["details"].(map[string]any); ok {
			serviceAccountName, _ := entityJsonDetails["displayName"].(string)
			return serviceAccountName
		}
	}

	if entityJSONName := getResourceNameFromEntityJSON(entityJSON); len(entityJSONName) > 0 {
		return filepath.Base(entityJSONName)
	}

	return filepath.Base(resourceID)
}

func getResourceNameFromEntityJSON(entityJSON string) (resourceName string) {

	entityJSONMap := make(map[string]any)

	if err := json.Unmarshal([]byte(entityJSON), &entityJSONMap); err != nil {
		return
	}

	if resourceName, _ = entityJSONMap["displayName"].(string); len(resourceName) <= 0 {
		if resourceName, _ = entityJSONMap["name"].(string); len(resourceName) <= 0 {
			resourceName, _ = entityJSONMap["entityName"].(string)
		}
	}

	return
}

func getNonDataResourcesElasticQuery() string {
	return `"` +
		common.AZURE_SUBSCRIPTION_RESOURCE_TYPE + `","` + common.AZURE_RG_RESOURCE_TYPE + `","` + common.AZURE_LOCATION_RESOURCE_TYPE + `","` + common.AZURE_ASSIGNEDROLE_RESOURCE_TYPE + `","` + common.AZURE_ROLEASSIGNMENT_RESOURCE_TYPE + `","` + common.AZURE_GROUPS_RESOURCE_TYPE + `","` + common.AZURE_POLICYSTATE_RESOURCE_TYPE + `","` +
		common.AZURE_POLICYDEFINITION_RESOURCE_TYPE + `","` + common.AWS_ACCOUNT_RESOURCE_TYPE + `","` + common.AWS_ORG_RESOURCE_TYPE + `","` + common.AWS_REGION_RESOURCE_TYPE + `","` + common.GCP_ORG_RESOURCE_TYPE + `","` + common.GCP_FOLDER_RESOURCE_TYPE + `","` + common.GCP_PROJECT_RESOURCE_TYPE + `","` + common.GCP_REGION_RESOURCE_TYPE +
		`","` + common.GCP_CONSTRAINT_RESOURCE_TYPE + `","` + common.OPENAI_ORG_RESOURCE_TYPE + `","` + common.OPENAI_PROJECT_RESOURCE_TYPE + `","` + common.AZURE_TENANT_RESOURCE_TYPE + `","` + common.AZURE_MGMTGRP_RESOURCE_TYPE + `","` + common.AWS_ORGUNIT_RESOURCE_TYPE + `"`
}

// Function not used
// func removeResourceContextItem(s *[]common.ResourceContextItem, r string) {

// 	var temp []common.ResourceContextItem

// 	temp = append(temp, *s...)

// 	for i, v := range temp {
// 		if v.Name == r {
// 			temp = append(temp[:i], temp[i+1:]...)
// 			break
// 		}
// 	}

// 	// Copying temp to s
// 	*s = append(temp, []common.ResourceContextItem{}...)

// 	return
// }

func IsUserResourceType(identityType string) bool {
	return identityType == common.AZURE_ADUSER_RESOURCE_TYPE ||
		identityType == common.GCP_USER_RESOURCE_TYPE ||
		identityType == common.AWS_IAM_USER_RESOURCE_TYPE ||
		identityType == common.AWS_SSOUSER_RESOURCE_TYPE ||
		identityType == common.AWS_ROOTUSER_RESOURCE_TYPE ||
		identityType == common.OPENAI_USER_RESOURCE_TYPE
}

func processActivityBatch(resourceContext *ResourceContext, docIDs []string) {
	if err := getActivityContextOfResource(resourceContext, docIDs); err != nil {
		logger.Print(logger.ERROR, "Failed to get activity context", []string{err.Error()})
		return
	}

	if err := getCodeContextOfResource(resourceContext, docIDs); err != nil {
		logger.Print(logger.ERROR, "Failed to get code context", []string{err.Error()})
		return
	}

	if err := getJiraContextOfResource(resourceContext, docIDs); err != nil {
		logger.Print(logger.ERROR, "Failed to get Jira context", []string{err.Error()})
		return
	}
}

func isBase64(str string) bool {
	str = strings.TrimSpace(str)

	if len(str)%4 != 0 {
		return false
	}

	_, err := base64.StdEncoding.DecodeString(str)
	return err == nil
}

func isGzipped(data []byte) bool {
	return len(data) > 2 && data[0] == 0x1F && data[1] == 0x8B
}

func decodeBase64Recursive(s string) string {
	decoded, err := base64.StdEncoding.DecodeString(s)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to Decode Base64 string")
		return ""
	}
	result := decoded

	if isBase64(string(result)) {
		if secondDecoded, err := base64.StdEncoding.DecodeString(string(result)); err == nil {
			result = secondDecoded
		}
	}

	if isGzipped(decoded) {
		reader, err := gzip.NewReader(bytes.NewReader(decoded))
		if err != nil {
			logger.Print(logger.ERROR, "Failed to create Gzip reader")
			return ""
		}
		defer reader.Close()

		var result bytes.Buffer
		if _, err := io.Copy(&result, reader); err != nil {
			logger.Print(logger.ERROR, "Failed to decompress Gzip data")
			return ""
		}
		return result.String()
	}

	return string(result)
}

func FormatContextValue(str string) (formattedStr string) {

	formattedStr = str

	if _, err := common.ParseAddress(str); err == nil {
		return
	}

	if len(str) == 2 {
		formattedStr = strings.ToUpper(str)
		return
	}

	if len(str) > 0 && unicode.IsLower([]rune(str)[0]) {
		formattedStr = common.ConvertToTitleCase(str)
		return
	}

	return
}
